"use client";

import React, { useCallback, useEffect, useState } from "react";
import axios from "axios";
import Swal from "sweetalert2";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import useDebounce from "@/hooks/useDebounce";
import { IoCloseOutline } from "react-icons/io5";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import Modal from "@mui/material/Modal";
import Box from "@mui/material/Box";
import { Button } from "@/components/ui-elements/button";
import InputGroup from "@/components/FormElements/InputGroup";
import { FaPlus } from "react-icons/fa";

const modalStyle = {
  position: "absolute",
  top: "50%",
  left: "50%",
  transform: "translate(-50%, -50%)",
  width: 400,
  bgcolor: "background.paper",
  boxShadow: 24,
  p: 4,
  borderRadius: "10px",
  outline: "none",
};

const Category = () => {
  const [showLoader, setShowLoader] = useState(false);
  const [categories, setCategories] = useState([]);
  const [formData, setFormData] = useState({ name: "" });

  const [modalState, setModalState] = useState({
    open: false,
    mode: "add",
    id: null,
  });

  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const [order, setOrder] = useState("asc");
  const [orderBy, setOrderBy] = useState("Name");

  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  const columns = [
    {
      id: "Name",
      label: "Category Name",
    },
    {
      id: "ShowUrlName",
      label: "URL Name",
    },
  ];

  const fetchCategories = useCallback(async () => {
    try {
      setShowLoader(true);
      const response = await axios.get("/api/category/getAllCategory", {
        params: {
          page: page + 1,
          length: rowsPerPage,
          q: debouncedSearchTerm,
          orderBy,
          orderDir: order,
          _: new Date().getTime(),
        },
        withCredentials: true,
      });

      if (response.status === 200) {
        setCategories(response.data.data);
        setTotalCount(
          response.data.pagination?.recordsFiltered ||
            response.data.data?.length ||
            0,
        );
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to fetch categories",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  }, [page, rowsPerPage, debouncedSearchTerm, orderBy, order]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleOpenAddModal = () => {
    setFormData({ name: "" });
    setModalState({
      open: true,
      mode: "add",
      id: null,
    });
  };

  const handleOpenEditModal = (rowData) => {
    setFormData({ name: rowData.Name });
    setModalState({
      open: true,
      mode: "edit",
      id: rowData.Id,
    });
  };

  const handleCloseModal = () => {
    setModalState({
      open: false,
      mode: "add",
      id: null,
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setModalState({ open: false });
    if (!formData.name.trim()) {
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: "Category name is required",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }

    try {
      setShowLoader(true);

      if (modalState.mode === "add") {
        const response = await axios.post(
          "/api/category/addCategory",
          {
            name: formData.name.trim(),
            showurlname: formData.name.trim(),
          },
          { withCredentials: true },
        );

        if (response.status === 200) {
          await Swal.fire({
            icon: "success",
            title: "Success",
            text: "Category added successfully",
            timer: 2000,
            showConfirmButton: false,
          });
        }
      } else {
        const response = await axios.put(
          `/api/category/EditCategory`,
          {
            Id: modalState.id,
            Name: formData.name.trim(),
          },
          { withCredentials: true },
        );

        if (response.status === 200) {
          await Swal.fire({
            icon: "success",
            title: "Updated",
            text: "Category updated successfully",
            timer: 2000,
            showConfirmButton: false,
          });
        }
      }

      setFormData({ name: "" });
      handleCloseModal();
      fetchCategories();
    } catch (error) {
      await Swal.fire({
        icon: "error",
        title: "Error",
        text:
          error?.response?.data?.error ||
          (modalState.mode === "add"
            ? "Failed to add category"
            : "Failed to update category"),
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  };

  const handleDeleteCategory = async (rowData) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      showCloseButton: true,
      confirmButtonColor: "#5750f1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        setShowLoader(true);
        const response = await axios.delete(`/api/category/DeleteCategory`, {
          data: { Id: rowData.Id },
          withCredentials: true,
        });

        if (response.status === 200) {
          await Swal.fire({
            icon: "success",
            title: "Deleted!",
            text: "Category has been deleted.",
            timer: 2000,
            showConfirmButton: false,
          });
          fetchCategories();
        }
      } catch (error) {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to delete category",
          timer: 3000,
          showConfirmButton: false,
        });
      } finally {
        setShowLoader(false);
      }
    }
  };

  const handleViewCategory = (rowData) => {
    window.open(
      `${process.env.NEXT_PUBLIC_BASE_URL_CLIENT_USER_SITE}/category/${rowData?.ShowUrlName?.toLowerCase()}`,
      "_blank",
    );
  };

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return (
    <>
      <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-2xl font-bold text-dark dark:text-white">
            {" "}
            Categories
          </h2>
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <Button
              type="button"
              label="Add Category"
              variant="primary"
              shape="rounded"
              icon={<FaPlus size={14} />}
              onClick={handleOpenAddModal}
            />
          </div>
        </div>
        <CustomDataTable
          isLoading={showLoader}
          columns={columns}
          rows={categories}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={setPage}
          onRowsPerPageChange={setRowsPerPage}
          totalCount={totalCount}
          order={order}
          orderBy={orderBy}
          onRequestSort={(event, property) => {
            const isAsc = orderBy === property && order === "asc";
            setOrder(isAsc ? "desc" : "asc");
            setOrderBy(property);
          }}
          onView={handleViewCategory}
          onEdit={handleOpenEditModal}
          onDelete={handleDeleteCategory}
        />
      </div>

      <Modal
        open={modalState.open}
        onClose={handleCloseModal}
        aria-labelledby="category-modal-title"
        aria-describedby="category-modal-description"
      >
        <Box sx={modalStyle}>
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold">
              {modalState.mode === "add" ? "Add New" : "Edit"} Category
            </h2>
            <button
              onClick={handleCloseModal}
              className="text-gray-500 hover:text-gray-700 focus:outline-none"
              aria-label="Close modal"
            >
              <IoCloseOutline size={24} />
            </button>
          </div>
          <form onSubmit={handleSubmit}>
            <InputGroup
              label="Category Name"
              type="text"
              name="name"
              value={formData.name}
              handleChange={handleChange}
              placeholder="Enter category name"
              required
            />
            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="submit"
                label={
                  showLoader
                    ? "Processing..."
                    : modalState.mode === "add"
                      ? "Add Category"
                      : "Update Category"
                }
                variant="primary"
                shape="rounded"
                className="flex-1"
                disabled={showLoader}
              />
            </div>
          </form>
        </Box>
      </Modal>
    </>
  );
};

export default Category;
