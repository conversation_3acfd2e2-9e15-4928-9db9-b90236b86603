import { prisma } from '../../../../lib/prisma';;
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const search = searchParams.get("q");
        const orderBy = searchParams.get("orderBy") || "CreatedAt";
        const orderDir = searchParams.get("orderDir") || "asc";

        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        }
        else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }
        const orderByClause = {
            [orderBy]: orderDir.toLowerCase() as 'asc' | 'desc',
        };
        let where: any = {
            Active: true,
        }
        if (search) {
            where.DisplayName = {
                contains: search,
                mode: 'insensitive'
            };
        }
        const users = await prisma.channals.findMany({
            where,
            skip,
            take: limit,
            orderBy: orderByClause,
            select: {
                Id: true,
                Name: true,
                DisplayName: true,
                ReportingDimensionId: true,
                // CreatedAt: true
            },
        });

        const dataWithCustomChannelId = users.map(channel => {
            const parts = (channel.Name ?? "").split('/');
            const customChannelId = parts[parts.length - 1];
            return {
                ...channel,
                CustomChannelId: customChannelId,
            };
        });

        const recordsTotal = await prisma.channals.count({
            where: {
                Active: true,
            }
        });
        const recordsFiltered = await prisma.channals.count({ where });

        // const recordsFiltered = await prisma.Channals.count({ where: {
        //         Active: true,
        //     } });

        return NextResponse.json({
            success: true,
            data: dataWithCustomChannelId,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                recordsFiltered,
                //recordsTotal,
                currentPageCount: dataWithCustomChannelId.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1)),
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error fetching users:", error);
        return NextResponse.json(
            { error: "Failed to fetch users" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}
