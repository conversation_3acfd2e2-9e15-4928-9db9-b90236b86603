import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const search = searchParams.get("q")?.toLowerCase();
        const orderBy = searchParams.get("orderBy") || "CreatedAt";
        const orderDir = (searchParams.get("orderDir") || "asc").toLowerCase() as 'asc' | 'desc';

        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        } else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }

        // Base query for pagination
        const users = await prisma.channals.findMany({
            where: {
                Active: true,
                ...(search && {
                    OR: [
                        { DisplayName: { contains: search, mode: 'insensitive' } },
                        { Name: { contains: search, mode: 'insensitive' } },
                    ],
                }),
            },
            skip,
            take: limit,
            orderBy: orderBy !== 'CustomChannelId' ? { [orderBy]: orderDir } : undefined,
            select: {
                Id: true,
                Name: true,
                DisplayName: true,
                ReportingDimensionId: true,
            },
        });

        // Add CustomChannelId
        let dataWithCustomChannelId = users.map(channel => {
            const parts = (channel.Name ?? "").split('/');
            const customChannelId = parts[parts.length - 1];
            return {
                ...channel,
                CustomChannelId: customChannelId,
            };
        });

        // If search should apply to CustomChannelId
        if (search) {
            dataWithCustomChannelId = dataWithCustomChannelId.filter(user =>
                user.CustomChannelId?.toLowerCase().includes(search) ||
                user.Name?.toLowerCase().includes(search) ||
                user.DisplayName?.toLowerCase().includes(search)
            );
        }

        // If orderBy is CustomChannelId, sort in-memory
        if (orderBy === 'CustomChannelId') {
            dataWithCustomChannelId.sort((a, b) => {
                const aVal = a.CustomChannelId || '';
                const bVal = b.CustomChannelId || '';
                return orderDir === 'asc'
                    ? aVal.localeCompare(bVal)
                    : bVal.localeCompare(aVal);
            });
        }

        const recordsTotal = await prisma.channals.count({
            where: { Active: true }
        });

        const recordsFiltered = await prisma.channals.count({
            where: {
                Active: true,
                ...(search && {
                    OR: [
                        { DisplayName: { contains: search, mode: 'insensitive' } },
                        { Name: { contains: search, mode: 'insensitive' } },
                    ],
                }),
            },
        });

        return NextResponse.json({
            success: true,
            data: dataWithCustomChannelId,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                recordsFiltered,
                currentPageCount: dataWithCustomChannelId.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1)),
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error fetching users:", error);
        return NextResponse.json(
            { error: "Failed to fetch users" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}
