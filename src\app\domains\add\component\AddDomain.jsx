"use client";
import React, { useEffect, useState } from "react";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import { Button } from "@/components/ui-elements/button";
import axios from "axios";
import Swal from "sweetalert2";
import InputGroup from "@/components/FormElements/InputGroup";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import { Dialog, DialogActions, DialogContent, DialogTitle, IconButton } from "@mui/material";
import { FaPlus } from "react-icons/fa";
import { TextAreaGroup } from "@/components/FormElements/InputGroup/text-area";
import { CloseIcon } from "@/assets/icons";

const DomainModal = ({
  isOpen,
  onClose,
  onSubmit,
  initialData,
  isEdit,
  channels,
}) => {
  const [formData, setFormData] = useState({
    Name: "",
    ShowUrlName: "",
    Prefix: "",
    ChannelId: "",
    CookieMinutes: "",
    StyleIdDm: "",
    StyleIdLm: "",
    HeadTagScript: "",
    HeadTagScriptLandingPage: "",
    HeadTagScriptSearchPage: "",
    GId: "",
    AWId: "",
    SendTo: "",
    ...(isEdit && initialData),
  });

  useEffect(() => {
    if (isEdit && initialData) {
      setFormData({
        Name: initialData.Name || "",
        ShowUrlName: initialData.ShowUrlName || "",
        Prefix: initialData.Prefix || "",
        ChannelId: initialData.ChannelId || "",
        CookieMinutes: initialData.CookieMinutes?.toString() || "",
        StyleIdDm: initialData.StyleIdDm?.toString() || "",
        StyleIdLm: initialData.StyleIdLm?.toString() || "",
        HeadTagScript: initialData.HeadTagScript || "",
        HeadTagScriptLandingPage: initialData.HeadTagScriptLandingPage || "",
        HeadTagScriptSearchPage: initialData.HeadTagScriptSearchPage || "",
        GId: initialData.GId || "",
        AWId: initialData.AWId || "",
        SendTo: initialData.SendTo || "",
      });
    } else {
      setFormData({
        Name: "",
        ShowUrlName: "",
        Prefix: "",
        ChannelId: "",
        CookieMinutes: "",
        StyleIdDm: "",
        StyleIdLm: "",
        HeadTagScript: "",
        HeadTagScriptLandingPage: "",
        HeadTagScriptSearchPage: "",
        GId: "",
        AWId: "",
        SendTo: "",
      });
    }
  }, [initialData, isEdit]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleChannelSelect = (item) => {
    setFormData((prev) => ({ ...prev, ChannelId: item?.Id ? item?.Id : "" }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSubmit(formData);
  };

  if (!isOpen) return null;

  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      fullWidth
      maxWidth="md"
      PaperProps={{
        sx: {
          maxHeight: "90vh",
        },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          color: "white",
          py: 2,
          px: 3,
        }}
        className="bg-primary text-white"
      >
        <span style={{ fontSize: "1.25rem", fontWeight: 500 }}>
          {isEdit ? "Edit Domain" : "Add Domain"}
        </span>
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            color: "white",
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers sx={{ py: 3, px: 3 }}>
        <form style={{ display: "flex", flexDirection: "column", gap: 16 }}>
          <div
            style={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
              gap: 20,
            }}
          >
            <InputGroup
              label="Domain Name"
              type="text"
              name="Name"
              value={formData.Name}
              handleChange={handleChange}
              placeholder="Enter Domain Name"
              required
            />

            <InputGroup
              label="Show URL Name"
              type="text"
              name="ShowUrlName"
              value={formData.ShowUrlName}
              handleChange={handleChange}
              placeholder="Enter URL-friendly name"
              required
            />

            <InputGroup
              label="Prefix (max 5 characters)"
              type="text"
              name="Prefix"
              value={formData.Prefix}
              handleChange={handleChange}
              placeholder="Enter prefix (e.g., fb, tw)"
              maxLength={5}
            />

            <SearchableDropdown
              label="Channel"
              options={channels.channelsList}
              placeholder="Select Channel"
              value={formData.ChannelId}
              onChange={handleChannelSelect}
              displayKey="DisplayName"
              idKey="Id"
              isLoading={channels.isLoading}
            />
            <InputGroup
              label="Style ID DM (Numeric)"
              type="number"
              name="StyleIdDm"
              value={formData.StyleIdDm}
              handleChange={handleChange}
              placeholder="Enter Style ID DM"
            />

            <InputGroup
              label="Style ID LM (Numeric)"
              type="number"
              name="StyleIdLm"
              value={formData.StyleIdLm}
              handleChange={handleChange}
              placeholder="Enter Style ID LM"
            />
            <InputGroup
              label="Cookie Duration (minutes)"
              type="number"
              name="CookieMinutes"
              value={formData.CookieMinutes}
              handleChange={handleChange}
              placeholder="Enter cookie duration in minutes"
            />
            <InputGroup
              label="Send To"
              name="SendTo"
              type="text"
              placeholder="Enter Send To"
              value={formData.SendTo}
              handleChange={handleChange}
            />
            <InputGroup
              label="AwId"
              name="AWId"
              type="text"
              placeholder="Enter AwId"
              value={formData.AWId}
              handleChange={handleChange}
            />
            <InputGroup
              label="GId"
              name="GId"
              type="text"
              placeholder="Enter GId"
              value={formData.GId}
              handleChange={handleChange}
            />
            <TextAreaGroup
              label="Head Tag Script"
              name="HeadTagScript"
              value={formData.HeadTagScript}
              handleChange={handleChange}
              rows={3}
            />
            <TextAreaGroup
              label="Head Tag Script Landing Page"
              name="HeadTagScriptLandingPage"
              value={formData.HeadTagScriptLandingPage}
              handleChange={handleChange}
              rows={3}
            />
            <TextAreaGroup
              label="Head Tag Script Search & Search Result Page"
              name="HeadTagScriptSearchPage"
              value={formData.HeadTagScriptSearchPage}
              handleChange={handleChange}
              rows={3}
            />
          </div>
        </form>
      </DialogContent>

      <DialogActions sx={{ px: 0, py: 2 }}>
        <Button
          type="submit"
          label={isEdit ? "Update Domain" : "Add Domain"}
          variant="primary"
          shape="rounded"
          onClick={handleSubmit}
        />
      </DialogActions>
    </Dialog>
  );
};

const AddDomain = () => {
  const [domains, setDomains] = useState([]);
  const [channels, setChannels] = useState({
    channelsList: [],
    isLoading: false,
  });
  const [loading, setLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editData, setEditData] = useState(null);

  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState("");
  const [order, setOrder] = useState("asc");
  const [orderBy, setOrderBy] = useState("Name");
  const [totalCount, setTotalCount] = useState(0);

  const columns = [
    { id: "Name", label: "Domain Name" },
    { id: "ShowUrlName", label: "Show URL Name" },
    { id: "Prefix", label: "Prefix" },
  ];

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await axios.get("/api/Domain/Get", {
        params: {
          page: page + 1,
          length: rowsPerPage,
          q: searchTerm,
          orderBy,
          orderDir: order,
        },
        withCredentials: true,
      });

      if (response.data.success) {
        setDomains(response.data.data);
        setTotalCount(response.data.pagination.recordsFiltered);
      } else {
        throw new Error(response.data.error || "Failed to fetch domains");
      }
    } catch (error) {
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error.message || "Failed to fetch domains",
        timer: 3000,
        showConfirmButton: false,
      });
      setDomains([]);
      setTotalCount(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [page, rowsPerPage, searchTerm, order, orderBy]);

  useEffect(() => {
    const fetchChannels = async () => {
      try {
        setChannels((prev) => ({ ...prev, isLoading: true }));

        const response = await axios.get("/api/Channals/GetDropdown", {
          withCredentials: true,
        });

        if (response.data.success) {
          setChannels({
            channelsList: response.data.data,
            isLoading: false,
          });
        } else {
          console.error("Failed to fetch channels:", response.data.error);
          setChannels((prev) => ({ ...prev, isLoading: false }));
          Swal.fire({
            icon: "error",
            title: "Error",
            text: response.data.error || "Failed to load channels",
            timer: 3000,
            showConfirmButton: false,
          });
        }
      } catch (error) {
        console.error("Error fetching channels:", error);
        setChannels((prev) => ({ ...prev, isLoading: false }));
        Swal.fire({
          icon: "error",
          title: "Error",
          text: error.message || "Failed to load channels",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    };
    if (
      isModalOpen &&
      channels.channelsList.length === 0 &&
      !channels.isLoading
    ) {
      fetchChannels();
    }
  }, [isModalOpen]);

  const handleDomainSubmit = async (formData) => {
    try {
      setLoading(true);
      let response;
      if (editData) {
        response = await axios.put(
          "/api/Domain/Edit",
          {
            Id: editData.Id,
            Name: formData.Name,
            ShowUrlName: formData.ShowUrlName,
            Prefix: formData.Prefix,
            ChannelId: formData.ChannelId,
            CookieMinutes: formData.CookieMinutes
              ? parseInt(formData.CookieMinutes)
              : null,
            StyleIdDm: formData.StyleIdDm ? parseInt(formData.StyleIdDm) : null,
            StyleIdLm: formData.StyleIdLm ? parseInt(formData.StyleIdLm) : null,
            HeadTagScript: formData.HeadTagScript,
            HeadTagScriptLandingPage: formData.HeadTagScriptLandingPage,
            HeadTagScriptSearchPage: formData.HeadTagScriptSearchPage,
            GId: formData.GId,
            AWId: formData.AWId,
            SendTo: formData.SendTo,
          },
          { withCredentials: true },
        );
      } else {
        response = await axios.post(
          "/api/Domain/Add",
          {
            Name: formData.Name,
            ShowUrlName: formData.ShowUrlName,
            Prefix: formData.Prefix,
            ChannelId: formData.ChannelId,
            CookieMinutes: formData.CookieMinutes
              ? parseInt(formData.CookieMinutes)
              : null,
            StyleIdDm: formData.StyleIdDm ? parseInt(formData.StyleIdDm) : null,
            StyleIdLm: formData.StyleIdLm ? parseInt(formData.StyleIdLm) : null,
            HeadTagScript: formData.HeadTagScript,
            HeadTagScriptLandingPage: formData.HeadTagScriptLandingPage,
            HeadTagScriptSearchPage: formData.HeadTagScriptSearchPage,
            GId: formData.GId,
            AWId: formData.AWId,
            SendTo: formData.SendTo,
          },
          { withCredentials: true },
        );
      }

      if (response.data.success) {
        Swal.fire(
          "Success",
          editData
            ? "Domain updated successfully"
            : "Domain added successfully",
          "success",
        );
        fetchData();
        setIsModalOpen(false);
        setEditData(null);
      } else {
        throw new Error(response.data.error || "Failed to save domain");
      }
    } catch (error) {
      Swal.fire(
        "Error",
        error?.response?.data?.error || "Failed to save domain",
        "error",
      );
    } finally {
      setLoading(false);
    }
  };

  const handleEditCategory = async (rowData) => {
  try {
    Swal.fire({
      title: 'Loading Domain Data',
      html: 'Please wait while we fetch the domain details...',
      allowOutsideClick: false,
      didOpen: () => {
        Swal.showLoading();
      }
    });

    const response = await axios.get(`/api/Domain/GetById?id=${rowData.Id}`);
    const domainData = response.data.data[0];

    setEditData({
      Id: domainData.Id,
      Name: domainData.Name,
      ShowUrlName: domainData.ShowUrlName,
      Prefix: domainData.Prefix || "",
      ChannelId: domainData.ChannelId || "lawyer",
      CookieMinutes: domainData.CookieMinutes?.toString() || "",
      StyleIdDm: domainData.StyleIdDm?.toString() || "",
      StyleIdLm: domainData.StyleIdLm?.toString() || "",
      HeadTagScript: domainData.HeadTagScript,
      HeadTagScriptLandingPage: domainData.HeadTagScriptLandingPage,
      HeadTagScriptSearchPage: domainData.HeadTagScriptSearchPage,
      GId: domainData.GId,
      AWId: domainData.AWId,
      SendTo: domainData.SendTo,
    });

    Swal.close();

    setIsModalOpen(true);

  } catch (error) {
    console.error("Error fetching domain data:", error);
    
    Swal.fire({
      title: 'Error',
      text: error.response?.data?.message || 
            'Failed to load domain data. Please try again.',
      icon: 'error',
      confirmButtonText: 'OK',
      confirmButtonColor: "#5750f1",

    });
    
  }
};

  const handleDeleteCategory = async (rowData) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      showCloseButton: true,
      confirmButtonColor: "#5750f1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        setLoading(true);
        const response = await axios.delete("/api/Domain/Delete", {
          data: { Id: rowData.Id },
          withCredentials: true,
        });

        if (response.data.success) {
          Swal.fire("Success", "Domain deleted successfully", "success");
          fetchData();
        } else {
          throw new Error(response.data.error || "Failed to delete domain");
        }
      } catch (error) {
        Swal.fire("Error", error.message || "Failed to delete domain", "error");
      } finally {
        setLoading(false);
      }
    }
  };

  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  const handleChangePage = (newPage) => {
    setPage(newPage);
  };

  return (
    <>
      <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-2xl font-bold text-dark dark:text-white">
            Domain
          </h2>
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <Button
              type="button"
              label="Add Domain"
              variant="primary"
              shape="rounded"
              icon={<FaPlus size={14} />}
              onClick={() => {
                setEditData(null);
                setIsModalOpen(true);
              }}
            />
          </div>
        </div>
        <CustomDataTable
          isLoading={loading}
          columns={columns}
          rows={domains}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={handleChangePage}
          onRowsPerPageChange={setRowsPerPage}
          totalCount={totalCount}
          order={order}
          orderBy={orderBy}
          onRequestSort={handleRequestSort}
          onEdit={handleEditCategory}
          onDelete={handleDeleteCategory}
        />
        <DomainModal
          isOpen={isModalOpen}
          onClose={() => {
            setIsModalOpen(false);
            setEditData(null);
          }}
          onSubmit={handleDomainSubmit}
          initialData={editData}
          isEdit={!!editData}
          channels={channels}
        />
      </div>
    </>
  );
};

export default AddDomain;
