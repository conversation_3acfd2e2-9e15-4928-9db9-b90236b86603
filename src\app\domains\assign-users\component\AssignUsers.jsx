"use client";
import React, { useCallback, useEffect, useState } from "react";
import axios from "axios";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import CustomDataTable from "../../../../components/DataTable/CustomDataTable";
import Swal from "sweetalert2";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";

const AssignUsers = () => {
  const [domains, setDomains] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedDomainId, setSelectedDomainId] = useState("");
  const [tableData, setTableData] = useState({
    loading: false,
    rows: [],
    totalCount: 0,
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [order, setOrder] = useState("asc");
  const [orderBy, setOrderBy] = useState("SubDomainName");

  const columns = [
    {
      id: "SubDomainName",
      label: "Subdomain Name",
      sortable: true,
    },
    {
      id: "SubDomainUrl",
      label: "URL",
      sortable: true,
    },
    {
      id: "DomainName",
      label: "Main Domain",
      sortable: true,
    },
    {
      id: "CreatedAt",
      label: "Created At",
      sortable: true,
    },
  ];

  useEffect(() => {
    const fetchDomains = async () => {
      setIsLoading(true);
      try {
        const response = await axios.get("/api/adminuser/GetDropdown", {
          headers: {
            Authorization: `Bearer ${localStorage.getItem("token")}`,
          },
        });
        if (response.data.success) {
          const domains = response.data.data;
          setDomains(domains);
        } else {
          console.error("Failed to fetch domains:", response.data.error);
        }
      } catch (error) {
        console.error("Error fetching domains:", error);
      } finally {
        setIsLoading(false);
      }
    };
    fetchDomains();
  }, []);

  const fetchAssignedSubDomains = useCallback(
    async ({ start, length, search, sortField, sortDirection }) => {
      if (!selectedDomainId) {
        return { data: [], total: 0 };
      }
      setTableData({ loading: true });
      try {
        const response = await axios.get("/api/AssignUserSubDomain/Get", {
          params: {
            Id: selectedDomainId,
            q: search,
            page: Math.floor(start / length) + 1,
            length: length,
            orderBy: sortField,
            orderDir: sortDirection,
          },
          withCredentials: true,
        });
        if (response.data.success) {
          const formattedData = (response.data.data || []).map((item) => ({
            ...item,
            id: item.Id,
            CreatedAt: new Date(item.CreatedAt).toLocaleString(),
          }));
          return {
            data: formattedData,
            total: response.data.pagination?.recordsFiltered || 0,
          };
        } else {
          return { data: [], total: 0 };
        }
      } catch (error) {
        console.error("Error fetching subdomains:", error);
        await Swal.fire({
          icon: "error",
          title: "Error",
          text:
            error?.response?.data?.error ||
            "Failed to load assigned subdomains",
          timer: 3000,
          showConfirmButton: false,
        });
        return { data: [], total: 0 };
      } finally {
        setTableData({ loading: false });
      }
    },
    [selectedDomainId],
  );

  const handleRequestSort = (event, property) => {
    const isAsc = orderBy === property && order === "asc";
    setOrder(isAsc ? "desc" : "asc");
    setOrderBy(property);
  };

  const handleDeleteCategory = async (rowData) => {
    const result = await Swal.fire({
      title: "Are you sure?",
      text: "You won't be able to revert this!",
      icon: "warning",
      showCancelButton: true,
      showCloseButton: true,
      confirmButtonColor: "#5750f1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, delete it!",
    });

    if (result.isConfirmed) {
      try {
        const token = localStorage.getItem("token");
        const response = await axios.delete(
          "/api/SubDomainUserMapping/Delete",
          {
            data: { Id: rowData.Id },
            headers: {
              Authorization: `Bearer ${token}`,
            },
            withCredentials: true,
          },
        );

        if (response.status === 200) {
          await Swal.fire({
            icon: "success",
            title: "Deleted!",
            text: "Mapping has been deleted.",
            timer: 2000,
            showConfirmButton: false,
          });

          const result = await fetchAssignedSubDomains({
            start: page * rowsPerPage,
            length: rowsPerPage,
            search: searchTerm,
            sortField: orderBy,
            sortDirection: order,
          });
          setTableData({
            rows: result.data,
            totalCount: result.total,
          });
        }
      } catch (error) {
        console.error(
          "Error deleting mapping:",
          error.message,
          error.response?.data,
        );
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to delete mapping",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    }
  };

  const handleDomainChange = (item) => {
    setSelectedDomainId(item?.Id ? item?.Id : "");
    setPage(0);
    setSearchTerm("");
    setOrder("asc");
    setOrderBy("SubDomainName");
  };

  useEffect(() => {
    const fetchData = async () => {
      if (!selectedDomainId) {
        setTableData({ rows: [], totalCount: 0 });
        return;
      }

      try {
        const result = await fetchAssignedSubDomains({
          start: page * rowsPerPage,
          length: rowsPerPage,
          search: searchTerm,
          sortField: orderBy,
          sortDirection: order,
        });
        setTableData({
          rows: result.data,
          totalCount: result.total,
        });
      } catch (error) {
        console.error("Error in fetchData:", error);
        setTableData({ rows: [], totalCount: 0 });
      }
    };

    fetchData();
  }, [
    selectedDomainId,
    searchTerm,
    page,
    rowsPerPage,
    order,
    orderBy,
    fetchAssignedSubDomains,
  ]);

  return (
    <>
      <div className="min-h-[600px] rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
        <h2 className="mb-4 text-2xl font-bold text-dark dark:text-white">
          Assigned Domains
        </h2>
        {isLoading ? (
          <div className="flex flex-1 justify-center py-8">
            <div className="h-8 w-8 animate-spin rounded-full border-4 border-primary border-t-transparent"></div>
          </div>
        ) : (
          <div className="mb-4 w-full min-w-[50%] max-w-[500px] sm:w-auto">
            <SearchableDropdown
              label="Select User"
              options={domains}
              placeholder="Select a User"
              value={selectedDomainId}
              onChange={handleDomainChange}
              displayKey="Name"
              displayKey2="Email"
              idKey="Id"
            />
          </div>
        )}

        {selectedDomainId && (
          <CustomDataTable
            isLoading={tableData.loading}
            key={selectedDomainId}
            columns={columns}
            rows={tableData.rows}
            searchTerm={searchTerm}
            onSearchChange={setSearchTerm}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={setPage}
            onRowsPerPageChange={setRowsPerPage}
            totalCount={tableData.totalCount}
            order={order}
            orderBy={orderBy}
            onRequestSort={handleRequestSort}
            onDelete={handleDeleteCategory}
          />
        )}
      </div>
    </>
  );
};

export default AssignUsers;
