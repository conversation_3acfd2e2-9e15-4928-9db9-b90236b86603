"use client";
import { cn } from "@/lib/utils";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import Tooltip from "@mui/material/Tooltip";
import { NAV_DATA } from "./data";
import { ArrowLeftIcon, ChevronUp } from "./icons";
import { MenuItem } from "./menu-item";
import { useSidebarContext } from "./sidebar-context";
import Swal from "sweetalert2";
import axios from "axios";
import { toast } from "react-toastify";
import LogoutIcon from '@mui/icons-material/Logout';

export function Sidebar() {
  const pathname = usePathname();
  const { isOpen, isMobile, setIsOpen, toggleSidebar, setIsCollapsed, isCollapsed } = useSidebarContext();
  const [user, setUser] = useState<{ Name: string; Email: string; ProfilePic?: string, img?: string } | null>(null);
  const [expandedItems, setExpandedItems] = useState<string[]>([]);

  const toggleExpanded = (title: string) => {
    setExpandedItems((prev) => (prev.includes(title) ? [] : [title]));
  };

  useEffect(() => {
    NAV_DATA.some((section) =>
      section.items.some((item) =>
        item.items.some((subItem) => {
          if (subItem.url === pathname) {
            if (!expandedItems.includes(item.title)) toggleExpanded(item.title);
            return true;
          }
        })
      )
    );
  }, [pathname]);

  const USER = {
    name: "John Smith",
    email: "<EMAIL>",
    img: "/images/user/user-03.png",
  };

  const handleLogOut = async () => {
    try {
      Swal.fire({
        title: "Are you sure?",
        text: "You will be logged out!",
        icon: "warning",
        showCancelButton: true,
        confirmButtonColor: "#5750f1",
        showCloseButton: true,
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, log out!",
      }).then(async (result) => {
        if (result.isConfirmed) {
          const response = await axios.post('/api/auth/Logout', {}, {
            withCredentials: true,
          });
          if (response.data.success) {
            localStorage.removeItem("user");
            toast.success(response.data.message || "Logged Out Successfully");
            window.location.href = '/';
          } else {
            toast.error("Logout failed");
          }
        }
      });
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("An error occurred during logout");
    }
  };

  useEffect(() => {
    const storedUser = localStorage.getItem("user");
    if (storedUser) {
      setUser(JSON.parse(storedUser));
    }
  }, []);

  return (
    <>
      {isMobile && isOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 transition-opacity duration-300"
          onClick={() => setIsOpen(false)}
          aria-hidden="true"
        />
      )}

      <aside
        className={cn(
          "fixed top-0 bottom-0 z-50 transition-all duration-300 bg-white dark:bg-gray-dark border-r border-gray-200 dark:border-gray-800",
          isMobile
            ? isOpen
              ? "w-64"
              : "hidden"
            : isCollapsed
              ? "w-24"
              : "w-70"
        )}
      >
        <div className="flex h-full flex-col py-6 pl-4 pr-2">
          <div className={`flex items-center px-2 ${isCollapsed ? 'justify-center' : 'justify-between'}`}>
            <Link
              href="/"
              prefetch={false}
              onClick={() => isMobile && toggleSidebar()}
              className={cn(
                "text-xl font-bold tracking-wide text-primary transition-all duration-200",
                isCollapsed && "hidden"
              )}
            >
              Domain<span className="text-blue-600">HQ</span>
            </Link>

            {!isMobile && (
              <button onClick={() => setIsCollapsed((prev) => !prev)}>
                <ArrowLeftIcon
                  className={cn(
                    "size-6 transition-transform",
                    isCollapsed ? "rotate-180" : "rotate-0"
                  )}
                />
              </button>
            )}
          </div>

          <div className="custom-scrollbar mt-6 flex-1 overflow-y-auto pr-2">
            {NAV_DATA.map((section, ind) => (
              <div key={ind} className="mb-4">
                <nav>
                  <ul className="space-y-1.5">
                    {section.items.map((item) => (
                      <li key={item.title}>
                        {item.items.length ? (
                          <div>
                            {isCollapsed ? (
                              <Tooltip
                                title={
                                  <div className="bg-white dark:bg-gray-dark shadow-lg rounded-md p-2">
                                    <ul className="space-y-1">
                                      {item.items.map((subItem) => (
                                        <li key={subItem.title}>
                                          <Link
                                            href={subItem.url}
                                            className={cn(
                                              "block px-4 py-2 text-sm text-gray-900 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 rounded",
                                              pathname === subItem.url && "bg-gray-100 dark:bg-gray-700"
                                            )}
                                            prefetch={false}
                                          >
                                            {subItem.title}
                                          </Link>
                                        </li>
                                      ))}
                                    </ul>
                                  </div>
                                }
                                placement="right"
                                arrow
                                componentsProps={{
                                  tooltip: {
                                    sx: {
                                      bgcolor: 'transparent',
                                      '& .MuiTooltip-arrow': {
                                        color: 'white',
                                      },
                                    },
                                  },
                                }}
                              >
                                <div>
                                  <MenuItem
                                    isActive={item.items.some(({ url }) => url === pathname)}
                                    onClick={() => toggleExpanded(item.title)}
                                    prefetch={false}
                                  >
                                    <item.icon className="size-6 shrink-0" />
                                  </MenuItem>
                                </div>
                              </Tooltip>
                            ) : (
                              <MenuItem
                                isActive={item.items.some(({ url }) => url === pathname)}
                                onClick={() => toggleExpanded(item.title)}
                                prefetch={false}
                              >
                                <item.icon className="size-6 shrink-0" />
                                <span>{item.title}</span>
                                <ChevronUp
                                  className={cn(
                                    "ml-auto rotate-180 transition-transform duration-200",
                                    expandedItems.includes(item.title) && "rotate-0"
                                  )}
                                />
                              </MenuItem>
                            )}

                            {expandedItems.includes(item.title) && !isCollapsed && (
                              <ul className="ml-9 space-y-1 pt-2" role="menu">
                                {item.items.map((subItem) => (
                                  <li key={subItem.title}>
                                    {isCollapsed ? (
                                      <Tooltip title={subItem.title} placement="right">
                                        <div>
                                          <MenuItem
                                            as="link"
                                            href={subItem.url}
                                            isActive={pathname === subItem.url}
                                            prefetch={false}
                                          >
                                            <span>{subItem.title}</span>
                                          </MenuItem>
                                        </div>
                                      </Tooltip>
                                    ) : (
                                      <MenuItem
                                        as="link"
                                        href={subItem.url}
                                        isActive={pathname === subItem.url}
                                        prefetch={false}
                                      >
                                        <span>{subItem.title}</span>
                                      </MenuItem>
                                    )}
                                  </li>
                                ))}
                              </ul>
                            )}
                          </div>
                        ) : (
                          isCollapsed ? (
                            <Tooltip title={item.title} placement="right">
                              <div>
                                <MenuItem
                                  className="flex items-center gap-3 py-2"
                                  as="link"
                                  href={item.url ?? ("/" + item.title.toLowerCase())}
                                  prefetch={false}
                                  isActive={pathname === item.url}
                                >
                                  <item.icon className="size-6 shrink-0" />
                                </MenuItem>
                              </div>
                            </Tooltip>
                          ) : (
                            <MenuItem
                              className="flex items-center gap-3 py-2"
                              as="link"
                              href={item.url ?? ("/" + item.title.toLowerCase())}
                              prefetch={false}
                              isActive={pathname === item.url}
                            >
                              <item.icon className="size-6 shrink-0" />
                              <span>{item.title}</span>
                            </MenuItem>
                          )
                        )}
                      </li>
                    ))}
                    {isCollapsed ? (
                      <Tooltip title="Log Out" placement="right">
                        <button
                          className="flex hover:bg-gray-100 px-3 py-3 rounded w-full justify-start"
                          onClick={handleLogOut}
                        >
                          <LogoutIcon />
                        </button>
                      </Tooltip>
                    ) : (
                      <button
                        className="flex hover:bg-gray-100 px-4 py-3 rounded w-full gap-3"
                        onClick={handleLogOut}
                      >
                        <LogoutIcon />
                        <span>Log Out</span>
                      </button>
                    )}
                  </ul>
                </nav>
              </div>
            ))}
          </div>

          <div className="mt-auto flex items-center gap-3 border-t pt-5 px-2 dark:border-gray-700">
            <img
              src={USER.img}
              alt={user?.Name || USER.name}
              className="h-10 w-10 rounded-full object-cover"
            />
            {!isCollapsed && (
              <div className="text-sm">
                <div className="font-medium text-gray-900 dark:text-white">
                  {user?.Name}
                </div>
                <div className="text-gray-500 dark:text-gray-400">{user?.Email}</div>
              </div>
            )}
          </div>
        </div>
      </aside>
    </>
  );
}