"use client";
import React, { useCallback, useEffect, useState } from "react";
import Swal from "sweetalert2";
import axios from "axios";
import { IoCloseOutline } from "react-icons/io5";
import Breadcrumb from "@/components/Breadcrumbs/Breadcrumb";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import { Box, Modal } from "@mui/material";
import InputGroup from "@/components/FormElements/InputGroup";
import MultiSelectDropdown from "@/components/FormElements/Dropdowns/MultiSelectDropdown";
import { Button } from "@/components/ui-elements/button";
import { FaPlus } from "react-icons/fa";

const StyleID = () => {
  const [showLoader, setShowLoader] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [showUserModal, setShowUserModal] = useState(false);
  const [editId, setEditId] = useState(null);
  const [formData, setFormData] = useState({
    StyleId: "",
    Name: "",
    Prefix: "",
    AssignUsers: [],
  });
  const [selectedStyleId, setSelectedStyleId] = useState("");
  const [mappings, setMappings] = useState([]);
  const [users, setUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [styleIds, setStyleIds] = useState([]);
  const [totalCount, setTotalCount] = useState(0);
  const [usersTotalCount, setUsersTotalCount] = useState(0);
  const [searchTerm, setSearchTerm] = useState("");
  const [searchTermUser, setSearchTermUser] = useState("");
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [order, setOrder] = useState("desc");
  const [orderBy, setOrderBy] = useState("CreatedAt");

  const columns = [
    {
      id: "Name",
      label: "Name",
    },
    {
      id: "Prefix",
      label: "Prefix",
    },
    {
      id: "UserCount",
      label: "Users",
    },
  ];

  const mappingColumns = [
    {
      id: "UserName",
      label: "User",
    },
    {
      id: "UserEmail",
      label: "Email",
    },
    {
      id: "UserType",
      label: "User Type",
    },
    {
      id: "CreatedAt",
      label: "Assigned On",
    },
  ];

  const fetchStyleIds = useCallback(async () => {
    try {
      setShowLoader(true);
      const response = await axios.get("/api/StyleIds/get", {
        params: {
          page: page + 1,
          length: rowsPerPage,
          q: searchTerm,
          orderBy,
          orderDir: order,
        },
        withCredentials: true,
      });

      const transformedData = response.data.data.map((item) => ({
        ...item,
        UserCount: item.UserMappings?.length || 0,
      }));

      setStyleIds(transformedData);
      setTotalCount(response.data.pagination.recordsFiltered);
    } catch (error) {
      console.error("Error fetching style IDs:", error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Error fetching style IDs",
        timer: 3000,
        showConfirmButton: false,
      });
      setStyleIds([]);
      setTotalCount(0);
    } finally {
      setShowLoader(false);
    }
  }, [page, rowsPerPage, searchTerm, order, orderBy]);

  const fetchMappingsData = useCallback(async () => {
    if (!selectedStyleId) {
      setMappings([]);
      return;
    }

    try {
      setShowLoader(true);
      const response = await axios.get("/api/StyleIdUserMapping/get", {
        params: {
          Id: selectedStyleId,
          q: searchTermUser, 
          page: page + 1,
          limit: rowsPerPage,
        },
        withCredentials: true,
      });

      const transformedData = response.data.data.map((item) => ({
        ...item,
        CreatedAt: new Date(item.CreatedAt).toLocaleString(),
      }));

      setMappings(transformedData);
      setUsersTotalCount(
        response.data.pagination?.recordsFiltered ||
          response.data.data?.length ||
          0,
      );
    } catch (error) {
      console.error("Error fetching user mappings:", error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to load user mappings",
        timer: 3000,
        showConfirmButton: false,
      });
      setMappings([]);
    } finally {
      setShowLoader(false);
    }
  }, [selectedStyleId, page, rowsPerPage, searchTermUser]);

  const fetchUsers = useCallback(async () => {
    try {
      setLoadingUsers(true);
      const response = await axios.get("/api/adminuser/GetDropdown", {
        withCredentials: true,
      });

      if (response?.status === 200) {
        const userData = response.data.data.map((user) => ({
          value: user.Id,
          label: `${user.Name} (${user.Email})`,
        }));
        setUsers(userData);
      }
    } catch (error) {
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: "Failed to load users. Please try again.",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setLoadingUsers(false);
    }
  }, []);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "Prefix" ? value.slice(0, 5) : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!formData.StyleId || !formData.Name || !formData.Prefix) {
      await Swal.fire({
        icon: "error",
        title: "Validation Error",
        text: "All fields are required.",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }

    if (isNaN(formData.StyleId)) {
      await Swal.fire({
        icon: "error",
        title: "Validation Error",
        text: "Style ID must be a number.",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }

    try {
      setShowLoader(true);
      const payload = {
        StyleId: formData.StyleId,
        Name: formData.Name,
        Prefix: formData.Prefix,
        AssignUsers: formData.AssignUsers,
      };

      if (editId) {
        await axios.put(
          `/api/StyleIds/edit`,
          { Id: editId, ...payload },
          { withCredentials: true },
        );
        await Swal.fire({
          icon: "success",
          title: "Success",
          text: "Style ID updated successfully.",
          timer: 2000,
          showConfirmButton: false,
        });
      } else {
        await axios.post("/api/StyleIds/add", payload, {
          withCredentials: true,
        });
        await Swal.fire({
          icon: "success",
          title: "Success",
          text: "Style ID added successfully.",
          timer: 2000,
          showConfirmButton: false,
        });
      }

      resetForm();
      setShowModal(false);
      fetchStyleIds();
      if (selectedStyleId) {
        fetchMappingsData();
      }
    } catch (error) {
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Error processing style ID.",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  };

  const resetForm = useCallback(() => {
    setFormData({
      StyleId: "",
      Name: "",
      Prefix: "",
      AssignUsers: [],
    });
    setEditId(null);
  }, []);

  const handleViewUsers = useCallback(async (rowData) => {
    setSelectedStyleId(rowData.Id);
    setShowUserModal(true);
  }, []);

  const handleEdit = useCallback((rowData) => {
    const assignedUserIds = Array.isArray(rowData.UserMappings)
      ? rowData.UserMappings.filter((userId) => userId !== null)
      : [];

    setFormData({
      StyleId: rowData.StyleId || "",
      Name: rowData.Name || "",
      Prefix: rowData.Prefix || "",
      AssignUsers: assignedUserIds,
    });
    setEditId(rowData.Id);
    setShowModal(true);
  }, []);

  const handleDelete = useCallback(
    async (rowData) => {
      const result = await Swal.fire({
        icon: "warning",
        title: "Confirm Deletion",
        text: "Are you sure you want to delete this Style ID?",
        showCancelButton: true,
        showCloseButton: true,
        confirmButtonColor: "#5750f1",
        cancelButtonColor: "#d33",
        confirmButtonText: "Yes, delete it!",
      });

      if (result.isConfirmed) {
        try {
          setShowLoader(true);
          await axios.delete("/api/StyleIds/Delete", {
            data: { Id: rowData.Id },
            withCredentials: true,
          });
          await Swal.fire({
            icon: "success",
            title: "Success",
            text: "Style ID deleted successfully.",
            timer: 2000,
            showConfirmButton: false,
          });
          fetchStyleIds();
          if (rowData.Id === selectedStyleId) {
            setSelectedStyleId("");
            setMappings([]);
          }
        } catch (error) {
          await Swal.fire({
            icon: "error",
            title: "Error",
            text: error?.response?.data?.error || "Error deleting style ID.",
            timer: 3000,
            showConfirmButton: false,
          });
        } finally {
          setShowLoader(false);
        }
      }
    },
    [selectedStyleId, fetchStyleIds],
  );

  const handleRemoveMapping = async (rowData) => {
    const result = await Swal.fire({
      icon: "warning",
      title: "Confirm Removal",
      text: "Are you sure you want to remove this user assignment?",
      showCancelButton: true,
      confirmButtonColor: "#5750f1",
      cancelButtonColor: "#d33",
      confirmButtonText: "Yes, remove it!",
    });

    if (result.isConfirmed) {
      try {
        setShowLoader(true);
        await axios.delete("/api/StyleIdUserMapping/delete", {
          data: { Id: rowData.Id },
          withCredentials: true,
        });
        await Swal.fire({
          icon: "success",
          title: "Success",
          text: "User assignment removed successfully",
          timer: 2000,
          showConfirmButton: false,
        });

        setShowUserModal(false);
        setSelectedStyleId("");
        setMappings([]);
        fetchStyleIds();
      } catch (error) {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text:
            error?.response?.data?.error || "Failed to remove user assignment",
          timer: 3000,
          showConfirmButton: false,
        });
      } finally {
        setShowLoader(false);
      }
    }
  };

  useEffect(() => {
    fetchStyleIds();
  }, [fetchStyleIds]);

  useEffect(() => {
    if (showUserModal) {
      fetchMappingsData();
    }
  }, [showUserModal, fetchMappingsData]);

  useEffect(() => {
    if (showModal) {
      fetchUsers();
    }
  }, [showModal, fetchUsers]);

  const styleForAddEditModal = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: { xs: "90%", sm: "70%", md: "50%", lg: "40%", xl: "30%" },
    bgcolor: "background.paper",
    border: "1px solid #000",
    boxShadow: 24,
    p: 4,
    borderRadius: "16px",
    maxHeight: { xs: "90vh", md: "95vh" },
    overflowY: "auto",
    scrollbarWidth: "none",
  };

  const styleForModal = {
    position: "absolute",
    top: "50%",
    left: "50%",
    transform: "translate(-50%, -50%)",
    width: { xs: "90%", sm: "70%" },
    bgcolor: "background.paper",
    border: "1px solid #000",
    boxShadow: 24,
    p: 4,
    borderRadius: "16px",
    maxHeight: { xs: "90vh", md: "95vh" },
    overflowY: "auto",
    scrollbarWidth: "none",
  };

  return (
    <>
      <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
        <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-2xl font-bold text-dark dark:text-white">
            Style IDs
          </h2>
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center">
            <Button
              type="button"
              label="Add Style ID"
              variant="primary"
              shape="rounded"
              icon={<FaPlus size={14} />}
              onClick={() => {
                resetForm();
                setShowModal(true);
              }}
            />
          </div>
        </div>
        <CustomDataTable
          isLoading={showLoader}
          columns={columns}
          rows={styleIds}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={setPage}
          onRowsPerPageChange={setRowsPerPage}
          totalCount={totalCount}
          order={order}
          orderBy={orderBy}
          onRequestSort={(event, property) => {
            const isAsc = orderBy === property && order === "asc";
            setOrder(isAsc ? "desc" : "asc");
            setOrderBy(property);
          }}
          onEdit={handleEdit}
          onDelete={handleDelete}
          handleUserCountClick={handleViewUsers}
        />
      </div>

      <Modal
        open={showModal}
        onClose={() => {
          setShowModal(false);
          resetForm();
        }}
        aria-labelledby="responsive-modal-title"
        aria-describedby="responsive-modal-description"
        sx={{ width: "100%" }}
      >
        <Box sx={styleForAddEditModal}>
          <div className="mb-4 flex items-center justify-between ">
            <h2 className="text-xl font-semibold">
              {editId ? "Edit Style ID" : "Add Style ID"}
            </h2>
            <button
              onClick={() => {
                setShowModal(false);
                resetForm();
              }}
              className="text-gray-500 hover:text-gray-700 focus:outline-none"
              aria-label="Close modal"
            >
              <IoCloseOutline size={24} />
            </button>
          </div>
          <form onSubmit={handleSubmit} className="space-y-8">
            <InputGroup
              label="Style ID (Numeric)"
              type="number"
              name="StyleId"
              value={formData.StyleId}
              handleChange={handleChange}
              placeholder="Enter Style ID"
              required
            />
            <InputGroup
              label="Name"
              type="text"
              name="Name"
              value={formData.Name}
              handleChange={handleChange}
              placeholder="Enter Name"
              required
            />
             <MultiSelectDropdown
              label="Assign Users"
              options={users}
              value={formData.AssignUsers}
              onChange={(selectedIds) =>
                setFormData({ ...formData, AssignUsers: selectedIds })
              }
              displayKey="label"
              idKey="value"
              placeholder="Select users..."
              showSelectAll={true}
            />
            <InputGroup
              label="Prefix (max 5 characters)"
              type="text"
              name="Prefix"
              value={formData.Prefix}
              handleChange={handleChange}
              placeholder="Enter Prefix (e.g., st)"
              required
            />
            <div className="flex justify-end space-x-2 py-4">
              <Button
                type="submit"
                label={showLoader ? "Loading..." : editId ? "Update" : "Submit"}
                variant="primary"
                shape="rounded"
                className="flex-1"
              />
            </div>
          </form>
        </Box>
      </Modal>

      <Modal
        open={showUserModal}
        onClose={() => {
          setShowUserModal(false);
          setSelectedStyleId("");
          setMappings([]);
          setSearchTermUser(""); 
        }}
        aria-labelledby="responsive-modal-title"
        aria-describedby="responsive-modal-description"
        sx={{ width: "100%" }}
      >
        <Box sx={styleForModal}>
          <div className="mb-4 flex items-center justify-between">
            <h2 className="text-xl font-semibold">
              User Mappings for Style ID
            </h2>
            <button
              onClick={() => {
                setShowUserModal(false);
                setSelectedStyleId("");
                setMappings([]);
                setSearchTermUser(""); 
              }}
              className="text-gray-500 hover:text-gray-700 focus:outline-none"
              aria-label="Close modal"
            >
              <IoCloseOutline size={24} />
            </button>
          </div>
          <CustomDataTable
            isLoading={showLoader}
            columns={mappingColumns}
            rows={mappings}
            searchTerm={searchTermUser}
            onSearchChange={setSearchTermUser}
            page={page}
            rowsPerPage={rowsPerPage}
            onPageChange={setPage}
            onRowsPerPageChange={setRowsPerPage}
            totalCount={usersTotalCount}
            order={order}
            orderBy={orderBy}
            onRequestSort={(event, property) => {
              const isAsc = orderBy === property && order === "asc";
              setOrder(isAsc ? "desc" : "asc");
              setOrderBy(property);
            }}
            onDelete={handleRemoveMapping}
          />
        </Box>
      </Modal>
    </>
  );
};

export default StyleID;