import { prisma } from '../../../../lib/prisma';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { email, password }: { email: string; password: string } = await req.json();

    const user = await prisma.adminUser.findUnique({ where: { Email: email } });

    if (!user || user.Block || user.Status === false) {
      return NextResponse.json({ error: 'Invalid credentials or account is blocked/inactive' }, { status: 401 });
    }

    const valid = await bcrypt.compare(password, user.Password ?? '');
    if (!valid) {
      return NextResponse.json({ error: 'Invalid credentials' }, { status: 401 });
    }

    const accessToken = jwt.sign(
      {
        Id: user.Id,
        Email: user.Email,
        User_Type: user.User_Type,
        ProfilePic: user.ProfilePic
      },
      process.env.JWT_SECRET as string,
      { expiresIn: '15m' }
    );

    const refreshToken = jwt.sign(
      {
        Id: user.Id,
        Email: user.Email,
        User_Type: user.User_Type,
        ProfilePic: user.ProfilePic
      },
      process.env.JWT_SECRET as string,
      { expiresIn: '7d' }
    );


    const cookieStore = await cookies();
    cookieStore.set('accessToken', accessToken, { httpOnly: true });
    cookieStore.set('refreshToken', refreshToken, { httpOnly: true })

    return NextResponse.json({
      success: true,
      accessToken
      // user: {
      //   Id: user.Id,
      //   Name: user.Name,
      //   Email: user.Email,
      //   User_Type: user.User_Type,
      //   ProfilePic: user.ProfilePic
      // }
    });
  } catch (err) {
    console.error("Login error:", err);
    return NextResponse.json({ error: 'Server error' }, { status: 500 });
  }
}
