
/* !!! This is code generated by Prisma. Do not edit directly. !!!
/* eslint-disable */

Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 6.7.0
 * Query Engine version: 3cff47a7f5d65c3ea74883f1d736e41d68ce91ed
 */
Prisma.prismaVersion = {
  client: "6.7.0",
  engine: "3cff47a7f5d65c3ea74883f1d736e41d68ce91ed"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.TransactionIsolationLevel = makeStrictEnum({
  ReadUncommitted: 'ReadUncommitted',
  ReadCommitted: 'ReadCommitted',
  RepeatableRead: 'RepeatableRead',
  Serializable: 'Serializable'
});

exports.Prisma.AdminUserScalarFieldEnum = {
  Id: 'Id',
  Name: 'Name',
  Email: 'Email',
  Password: 'Password',
  User_Type: 'User_Type',
  Block: 'Block',
  Number: 'Number',
  Status: 'Status',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt',
  DisplayName: 'DisplayName',
  ProfilePic: 'ProfilePic',
  AccessExpiration: 'AccessExpiration',
  IsDeleted: 'IsDeleted'
};

exports.Prisma.AdminUserSettingScalarFieldEnum = {
  Id: 'Id',
  LmStyleId: 'LmStyleId',
  DmStyleId: 'DmStyleId',
  ChannalId: 'ChannalId',
  PubId: 'PubId',
  AdsAccountId: 'AdsAccountId',
  AdsClientId: 'AdsClientId',
  HeadTagJSON: 'HeadTagJSON',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt',
  CountAdsClick: 'CountAdsClick',
  GoogleClientId: 'GoogleClientId',
  GoogleClientSecret: 'GoogleClientSecret',
  GoogleTokens: 'GoogleTokens'
};

exports.Prisma.Ads_AccountDetailsScalarFieldEnum = {
  SNo: 'SNo',
  AccountId: 'AccountId',
  ResourceName: 'ResourceName',
  ClientCustomer: 'ClientCustomer',
  Level: 'Level',
  TimeZone: 'TimeZone',
  Manager: 'Manager',
  DescriptiveName: 'DescriptiveName',
  CurrencyCode: 'CurrencyCode'
};

exports.Prisma.Ads_CampaignDetailsScalarFieldEnum = {
  SNo: 'SNo',
  CampaignId: 'CampaignId',
  ActiveViewCPM: 'ActiveViewCPM',
  ActiveViewCTR: 'ActiveViewCTR',
  ActiveViewImpressions: 'ActiveViewImpressions',
  ActiveViewMeasurability: 'ActiveViewMeasurability',
  ActiveViewMeasurableCostMicros: 'ActiveViewMeasurableCostMicros',
  ActiveViewMeasurableImpressions: 'ActiveViewMeasurableImpressions',
  ActiveViewViewAbility: 'ActiveViewViewAbility',
  AllConversionsValue: 'AllConversionsValue',
  AllConversionsValueByConversionDate: 'AllConversionsValueByConversionDate',
  AllConversions: 'AllConversions',
  AllConversionsByConversionDate: 'AllConversionsByConversionDate',
  AverageCost: 'AverageCost',
  AverageCPC: 'AverageCPC',
  AverageCPM: 'AverageCPM',
  Clicks: 'Clicks',
  ConversionsFromInteractionsRate: 'ConversionsFromInteractionsRate',
  ConversionsValue: 'ConversionsValue',
  ConversionsValueByConversionDate: 'ConversionsValueByConversionDate',
  Conversions: 'Conversions',
  ConversionsByConversionDate: 'ConversionsByConversionDate',
  CostMicros: 'CostMicros',
  CostPerAllConversions: 'CostPerAllConversions',
  CostPerConversion: 'CostPerConversion',
  CostPerCurrentModelAttributedConversion: 'CostPerCurrentModelAttributedConversion',
  CTR: 'CTR',
  CurrentModelAttributedConversions: 'CurrentModelAttributedConversions',
  CurrentModelAttributedConversionsFromInteractionsRate: 'CurrentModelAttributedConversionsFromInteractionsRate',
  CurrentModelAttributedConversionsFromInteractionsValuePerIntera: 'CurrentModelAttributedConversionsFromInteractionsValuePerIntera',
  CurrentModelAttributedConversionsValue: 'CurrentModelAttributedConversionsValue',
  CurrentModelAttributedConversionsValuePerCost: 'CurrentModelAttributedConversionsValuePerCost',
  Engagements: 'Engagements',
  Impressions: 'Impressions',
  InteractionRate: 'InteractionRate',
  Interactions: 'Interactions',
  InvalidClickRate: 'InvalidClickRate',
  InvalidClicks: 'InvalidClicks',
  GeneralInvalidClickRate: 'GeneralInvalidClickRate',
  GeneralInvalidClicks: 'GeneralInvalidClicks',
  AverageTargetCpaMicros: 'AverageTargetCpaMicros',
  ValuePerAllConversions: 'ValuePerAllConversions',
  ValuePerAllConversionsByConversionDate: 'ValuePerAllConversionsByConversionDate',
  ValuePerConversion: 'ValuePerConversion',
  ValuePerConversionsByConversionDate: 'ValuePerConversionsByConversionDate',
  ValuePerCurrentModelAttributedConversion: 'ValuePerCurrentModelAttributedConversion',
  SegmentDate: 'SegmentDate'
};

exports.Prisma.Ads_CampaignsScalarFieldEnum = {
  SNo: 'SNo',
  AccountId: 'AccountId',
  CampaignId: 'CampaignId',
  CustomerResourceName: 'CustomerResourceName',
  CampaignResourceName: 'CampaignResourceName',
  Status: 'Status',
  AdvertisingChannelType: 'AdvertisingChannelType',
  NetworkSettings: 'NetworkSettings',
  ExperimentType: 'ExperimentType',
  ServingStatus: 'ServingStatus',
  BiddingStrategyType: 'BiddingStrategyType',
  GeoTargetTypeSetting: 'GeoTargetTypeSetting',
  PaymentMode: 'PaymentMode',
  Name: 'Name',
  StartDate: 'StartDate',
  EndDate: 'EndDate',
  OptimizationScore: 'OptimizationScore',
  PrimaryStatus: 'PrimaryStatus',
  PrimaryStatusReasons: 'PrimaryStatusReasons',
  BrandGuidelinesEnabled: 'BrandGuidelinesEnabled',
  SegmentDate: 'SegmentDate'
};

exports.Prisma.Ads_CampaignsBudgetScalarFieldEnum = {
  SNo: 'SNo',
  CampaignId: 'CampaignId',
  CampaignBudgetId: 'CampaignBudgetId',
  CampaignBudgetResourceName: 'CampaignBudgetResourceName',
  Name: 'Name',
  Status: 'Status',
  AmountMicros: 'AmountMicros',
  DeliveryMethod: 'DeliveryMethod',
  ExplicitlyShared: 'ExplicitlyShared',
  HasRecommendedBudget: 'HasRecommendedBudget',
  Period: 'Period',
  ReferenceCount: 'ReferenceCount',
  Type: 'Type',
  SegmentDate: 'SegmentDate'
};

exports.Prisma.ArticleCampaignMappingsScalarFieldEnum = {
  Id: 'Id',
  ArticleId: 'ArticleId',
  CreatedAt: 'CreatedAt',
  CampaignId: 'CampaignId'
};

exports.Prisma.ArticleDetailsScalarFieldEnum = {
  Id: 'Id',
  Title: 'Title',
  Category: 'Category',
  Url: 'Url',
  Description: 'Description',
  MetaTitle: 'MetaTitle',
  MetaDescription: 'MetaDescription',
  MetaKeys: 'MetaKeys',
  StyleId: 'StyleId',
  User_Id_Settings: 'User_Id_Settings',
  AdRelatedSearches: 'AdRelatedSearches',
  Remark: 'Remark',
  Image: 'Image',
  Published: 'Published',
  ShowArticle: 'ShowArticle',
  ShowUrlName: 'ShowUrlName',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt',
  ShowsAds: 'ShowsAds',
  CustomChannal: 'CustomChannal',
  Domain: 'Domain',
  SubDomain: 'SubDomain',
  ShortDescription: 'ShortDescription',
  IsDeleted: 'IsDeleted'
};

exports.Prisma.CategoryScalarFieldEnum = {
  Id: 'Id',
  Name: 'Name',
  ShowUrlName: 'ShowUrlName',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt',
  IsDeleted: 'IsDeleted'
};

exports.Prisma.ChannalsScalarFieldEnum = {
  Id: 'Id',
  Name: 'Name',
  ReportingDimensionId: 'ReportingDimensionId',
  DisplayName: 'DisplayName',
  Active: 'Active',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt'
};

exports.Prisma.DomainScalarFieldEnum = {
  Id: 'Id',
  Name: 'Name',
  ShowUrlName: 'ShowUrlName',
  SubDomains: 'SubDomains',
  Articles: 'Articles',
  CreatedBy: 'CreatedBy',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt',
  V: 'V',
  Prefix: 'Prefix',
  ChannelId: 'ChannelId',
  CookieMinutes: 'CookieMinutes',
  StyleIdDm: 'StyleIdDm',
  StyleIdLm: 'StyleIdLm',
  IsDeleted: 'IsDeleted',
  HeadTagScript: 'HeadTagScript',
  HeadTagScriptLandingPage: 'HeadTagScriptLandingPage',
  HeadTagScriptSearchPage: 'HeadTagScriptSearchPage',
  GId: 'GId',
  AWId: 'AWId',
  SendTo: 'SendTo'
};

exports.Prisma.SavedReportScalarFieldEnum = {
  Id: 'Id',
  Name: 'Name',
  Title: 'Title',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt'
};

exports.Prisma.StyleIdUserMappingsScalarFieldEnum = {
  Id: 'Id',
  UserId: 'UserId',
  CreatedAt: 'CreatedAt',
  StyleId: 'StyleId',
  V: 'V'
};

exports.Prisma.StyleIdsScalarFieldEnum = {
  Id: 'Id',
  StyleId: 'StyleId',
  Name: 'Name',
  Prefix: 'Prefix',
  CreatedBy: 'CreatedBy',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt',
  V: 'V',
  DomainId: 'DomainId',
  IsDeleted: 'IsDeleted'
};

exports.Prisma.SubDomainScalarFieldEnum = {
  Id: 'Id',
  Name: 'Name',
  Url: 'Url',
  Domain: 'Domain',
  HeadTag: 'HeadTag',
  CId: 'CId',
  Articles: 'Articles',
  CreatedBy: 'CreatedBy',
  CreatedAt: 'CreatedAt',
  UpdatedAt: 'UpdatedAt',
  V: 'V',
  AccountId: 'AccountId',
  IsDeleted: 'IsDeleted',
  HeadTagScript: 'HeadTagScript',
  HeadTagScriptLandingPage: 'HeadTagScriptLandingPage',
  HeadTagScriptSearchPage: 'HeadTagScriptSearchPage',
  GId: 'GId',
  AWId: 'AWId',
  SendTo: 'SendTo'
};

exports.Prisma.SubDomainUserMappingsScalarFieldEnum = {
  Id: 'Id',
  SubDomainId: 'SubDomainId',
  UserId: 'UserId',
  CreatedAt: 'CreatedAt',
  V: 'V'
};

exports.Prisma.Ads_CampaignCountryDetailsScalarFieldEnum = {
  SNo: 'SNo',
  CampaignId: 'CampaignId',
  CountryId: 'CountryId',
  Clicks: 'Clicks',
  Impressions: 'Impressions',
  AverageCPC: 'AverageCPC',
  CostMicros: 'CostMicros',
  Conversions: 'Conversions',
  SegmentDate: 'SegmentDate'
};

exports.Prisma.Ads_CountryMasterScalarFieldEnum = {
  SNo: 'SNo',
  CountryId: 'CountryId',
  Name: 'Name'
};

exports.Prisma.CampaignBudgetsScalarFieldEnum = {
  SNo: 'SNo',
  CampaignId: 'CampaignId',
  BudgetId: 'BudgetId',
  AmountMicros: 'AmountMicros',
  SegmentDate: 'SegmentDate'
};

exports.Prisma.CampaignsScalarFieldEnum = {
  SNo: 'SNo',
  CampaignId: 'CampaignId',
  AccountId: 'AccountId',
  Name: 'Name',
  Status: 'Status',
  Type: 'Type',
  SegmentDate: 'SegmentDate'
};

exports.Prisma.GetGeoTargetConstantsScalarFieldEnum = {
  SNo: 'SNo',
  CountryId: 'CountryId',
  Name: 'Name'
};

exports.Prisma.GetGeographicViewScalarFieldEnum = {
  SNo: 'SNo',
  CampaignId: 'CampaignId',
  CountryId: 'CountryId',
  Clicks: 'Clicks',
  Impressions: 'Impressions',
  AverageCPC: 'AverageCPC',
  CostMicros: 'CostMicros',
  Conversions: 'Conversions',
  SegmentDate: 'SegmentDate'
};

exports.Prisma.RevenueScalarFieldEnum = {
  Id: 'Id',
  Date: 'Date',
  StyleId: 'StyleId',
  ChannalId: 'ChannalId',
  Country: 'Country',
  PlatfromType: 'PlatfromType',
  EstimatedEarnings: 'EstimatedEarnings',
  Impressions: 'Impressions',
  ImpressionsRpm: 'ImpressionsRpm',
  Clicks: 'Clicks',
  ImpressionsCtr: 'ImpressionsCtr',
  CostPerClick: 'CostPerClick'
};

exports.Prisma.QueryAnalyticsScalarFieldEnum = {
  Id: 'Id',
  BrowserId: 'BrowserId',
  IpAddress: 'IpAddress',
  URL: 'URL',
  Keyword: 'Keyword',
  Count: 'Count',
  AdsClickCounter: 'AdsClickCounter',
  Country: 'Country',
  State: 'State',
  Created_At: 'Created_At',
  Domain: 'Domain'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.NullableJsonNullValueInput = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};

exports.Prisma.NullsOrder = {
  first: 'first',
  last: 'last'
};

exports.Prisma.JsonNullValueFilter = {
  DbNull: Prisma.DbNull,
  JsonNull: Prisma.JsonNull,
  AnyNull: Prisma.AnyNull
};


exports.Prisma.ModelName = {
  AdminUser: 'AdminUser',
  AdminUserSetting: 'AdminUserSetting',
  Ads_AccountDetails: 'Ads_AccountDetails',
  Ads_CampaignDetails: 'Ads_CampaignDetails',
  Ads_Campaigns: 'Ads_Campaigns',
  Ads_CampaignsBudget: 'Ads_CampaignsBudget',
  ArticleCampaignMappings: 'ArticleCampaignMappings',
  ArticleDetails: 'ArticleDetails',
  Category: 'Category',
  Channals: 'Channals',
  Domain: 'Domain',
  SavedReport: 'SavedReport',
  StyleIdUserMappings: 'StyleIdUserMappings',
  StyleIds: 'StyleIds',
  SubDomain: 'SubDomain',
  SubDomainUserMappings: 'SubDomainUserMappings',
  Ads_CampaignCountryDetails: 'Ads_CampaignCountryDetails',
  Ads_CountryMaster: 'Ads_CountryMaster',
  CampaignBudgets: 'CampaignBudgets',
  Campaigns: 'Campaigns',
  getGeoTargetConstants: 'getGeoTargetConstants',
  getGeographicView: 'getGeographicView',
  Revenue: 'Revenue',
  QueryAnalytics: 'QueryAnalytics'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }

        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
