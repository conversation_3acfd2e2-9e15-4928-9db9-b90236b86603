"use client";
import React, { useCallback, useEffect, useRef, useState } from "react";
import dynamic from "next/dynamic";
import axios from "axios";
import { IoCloseOutline } from "react-icons/io5";
import { FaPlus } from "react-icons/fa";
const JoditEditor = dynamic(() => import("jodit-react"), { ssr: false });
import { convertToBase64, fetchImage } from "@/utils/functions";
// const Swal = dynamic(() => import("sweetalert2"), { ssr: false });
import Swal from "sweetalert2";
import CustomDataTable from "@/components/DataTable/CustomDataTable";
import SearchableDropdown from "@/components/FormElements/Dropdowns/SearchableDropdown";
import MultiSelectDropdown from "@/components/FormElements/Dropdowns/MultiSelectDropdown";
import InputGroup from "@/components/FormElements/InputGroup";
import { TextAreaGroup } from "@/components/FormElements/InputGroup/text-area";
import { Box, Modal } from "@mui/material";
import { But<PERSON> } from "@/components/ui-elements/button";
import { decodeJWT } from "@/utils/functions";
// JoditEditor configuration
const joditConfig = {
  height: 300,
  minHeight: 200,
  maxHeight: 300,
  uploader: { insertImageAsBase64URI: true },
  toolbarAdaptive: false,
  buttons: [
    "bold",
    "italic",
    "underline",
    "strikethrough",
    "|",
    "ul",
    "ol",
    "|",
    "outdent",
    "indent",
    "|",
    "font",
    "fontsize",
    "brush",
    "paragraph",
    "|",
    "image",
    "video",
    "table",
    "link",
    "|",
    "align",
    "undo",
    "redo",
    "|",
    "hr",
    "eraser",
    "copyformat",
    "|",
    "source",
    "fullsize",
  ],
  style: {
    border: "1px solid #e5e7eb",
    borderRadius: "8px",
    overflowY: "auto",
  },
};

const shortJoditConfig = {
  height: 100,
  minHeight: 120,
  maxHeight: 150,
  uploader: { insertImageAsBase64URI: true },
  toolbarAdaptive: false,
  buttons: ["bold", "italic", "underline", "ul", "ol", "link", "erase"],
  style: {
    border: "1px solid #e5e7eb",
    borderRadius: "8px",
    overflowY: "auto",
  },
};

const fieldMapping = {
  title: "Title",
  url: "Url",
  description: "Description",
  shortDescription: "ShortDescription",
  metatitle: "MetaTitle",
  metadescription: "MetaDescription",
  metakeys: "MetaKeys",
  hashtag: "Hashtag",
  customChannal: "CustomChannal",
  styleId: "StyleId",
  adrelatedsearches: "AdRelatedSearches",
  remark: "Remark",
  category: "Category",
  user_id_settings: "UserId",
  domain: "Domain",
  subdomain: "SubDomain",
  published: "Published",
  showArticle: "ShowArticle",
  showAds: "ShowsAds",
  image: "Image",
  domainName: "DomainName", // Added
  subDomainName: "SubDomainName", // Added
};

const reverseFieldMapping = {
  title: "Title",
  url: "url",
  description: "description",
  shortDescription: "shortDescription",
  metatitle: "metatitle",
  metadescription: "metadescription",
  metakeys: "metakeys",
  hashtag: "hashtag",
  customChannal: "customChannal",
  styleId: "styleId",
  adrelatedsearches: "adrelatedsearches",
  remark: "remark",
  category: "category",
  userName: "user_id_settings",
  domain: "domain",
  subdomain: "subdomain",
  published: "Published",
  showArticle: "ShowArticle",
  showAds: "ShowsAds",
  image: "image",
  categoryName: "Category",
  customChannalName: "CustomChannal",
  channelId: "CustomChannalId",
  campaignCount: "CampaignCount", // Added for campaigns column sorting
  status: "Published",
  userName: "UserName",
  domainName: "DomainName",
  subDomainName: "SubDomainName",
};

const ArticlePage = () => {
  const [categories, setCategories] = useState([]);
  const [modalShow, setModalShow] = useState(false);
  const [articles, setArticles] = useState([]);
  const [editId, setEditId] = useState(null);
  const [showLoader, setShowLoader] = useState(false);
  const [published, setPublished] = useState(true);
  const [showArticle, setShowArticle] = useState(false);
  const [showAds, setShowAds] = useState(false);
  const [formdataImage, setFormdataImage] = useState("");
  const [assignChannels, setAssignChannels] = useState([]);
  const [settingsData, setSettingsData] = useState({});
  const [base64Image, setBase64Image] = useState("");
  const [users, setUsers] = useState([]);
  const [selectedUser, setSelectedUser] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("");
  const [isSuperAdmin, setIsSuperAdmin] = useState(true);
  const [isPartner, setIsPartner] = useState(false);
  const [domains, setDomains] = useState([]);
  const [selectedDomain, setSelectedDomain] = useState("");
  const [subdomains, setSubdomains] = useState([]);
  const [selectedSubdomain, setSelectedSubdomain] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [editSlugMode, setEditSlugMode] = useState(false);
  const [campaigns, setCampaigns] = useState([]);
  const [selectedCampaignIds, setSelectedCampaignIds] = useState([]);
  const [styleIds, setStyleIds] = useState([]);
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [totalCount, setTotalCount] = useState(0);
  const dropdownRef = useRef(null);
  const [description, setDescription] = useState("");
  const [shortDescription, setShortDescription] = useState("");
  const [orderBy, setOrderBy] = useState("");
  const [order, setOrder] = useState("asc");

  let accessToken = localStorage.getItem("accessToken");
  const userData = accessToken ? decodeJWT(accessToken) : null;;

  const defaultValuesForm = {
    title: { val: "", err: "" },
    category: { val: "", err: "" },
    url: { val: "", err: "" },
    description: { val: "", err: "" },
    shortDescription: { val: "", err: "" },
    metatitle: { val: "", err: "" },
    metadescription: { val: "", err: "" },
    metakeys: { val: "", err: "" },
    customChannal: { val: "", err: "" },
    hashtag: { val: "", err: "" },
    styleId: { val: "", err: "" },
    adrelatedsearches: { val: "7", err: "" },
    remark: { val: "", err: "" },
    campaigns: { val: [], err: "" },
    domain: { val: "", err: "" },
    subdomain: { val: "", err: "" },
  };
  const optionalFields = [
    "remark",
    "metatitle",
    "metadescription",
    "metakeys",
    "hashtag",
  ];
  const [formData, setFormData] = useState(defaultValuesForm);

  const validateField = (name, value) => {
    if (optionalFields.includes(name)) return "";
    return value
      ? ""
      : `${name.replace(/([A-Z])/g, " $1").trim()} field is required.`;
  };

  const columns = [
    { id: "title", label: "Title" },
    ...(isSuperAdmin ? [{ id: "userName", label: "User Name" }] : []),
    { id: "categoryName", label: "Category" },
    { id: "showAds", label: "Show Ads" },
    { id: "customChannalName", label: "Custom Channel" },
    { id: "channelId", label: "Channel ID" },
    { id: "campaignCount", label: "Campaigns" }, // New campaigns column
    { id: "status", label: "Status" },
    { id: "domainName", label: "Domain Name" },
    { id: "subDomainName", label: "SubDomain Name" },
  ];
  const mapResponseToFrontend = (article) => {
    return {
      id: article.Id || article.id,
      title: article.Title || article.title || "No Title",
      url: article.Url || article.url || "",
      description: article.Description || article.description || "",
      shortDescription:
        article.ShortDescription || article.shortDescription || "",
      metatitle: article.MetaTitle || article.metatitle || "",
      metadescription: article.MetaDescription || article.metadescription || "",
      metakeys: article.MetaKeys || article.metakeys || "",
      hashtag: article.Hashtag || article.hashtag || "",
      customChannal:
        article.CustomChannalId &&
        (article.ChannelName || article.customChannalName)
          ? {
              Id: article.CustomChannalId,
              displayName: article.ChannelName || article.customChannalName,
              reportingDimensionId: `ga:${article.CustomChannalId}`,
            }
          : null,
      styleId: article.StyleId || article.styleId || "",
      adrelatedsearches:
        article.AdRelatedSearches || article.adrelatedsearches || "7",
      remark: article.Remark || article.remark || "",
      published: !!article.Published || !!article.published,
      showArticle: !!article.ShowArticle || !!article.showArticle,
      showAds: !!article.ShowsAds || !!article.showsAds,
      image: article.Image || article.image || "",
      userId: article.User_Id_Settings || article.userId || "",
      userName: article.UserName || article.userName || "Unknown",
      category: article.CategoryName
        ? {
            Id: article.CategoryId || article.category?.Id,
            Name: article.CategoryName,
          }
        : null,
      domain: article.DomainName
        ? {
            Id: article.DomainId || article.domain?.Id,
            name: article.DomainName,
            showUrlName: article.DomainName,
          }
        : null,
      subdomain: article.SubDomainName
        ? {
            Id: article.SubDomainId || article.subdomain?.Id,
            name: article.SubDomainName,
          }
        : null,
      campaigns:
        article.Campaigns?.map((c) => ({
          SNo: c.SNo,
          Name: c.Name,
          Description: c.Description || "",
        })) || [],
      categoryName: article.CategoryName || "No category",
      showAds: article.ShowsAds || article.showsAds ? "True" : "False",
      customChannalName:
        article.ChannelName || article.customChannalName || "No channel",
      channelId: article.CustomChannalId || "No ID",
      status: article.Published || article.published ? "Published" : "Draft",
      campaignCount: article.CampaignCount || 0, // New campaign count field
      domainName: article.DomainName || "No domain",
      subDomainName: article.SubDomainName || "No subdomain",
    };
  };
  // const fetchArticles = useCallback(async () => {
  //   try {
  //     setShowLoader(true);
  //     let url = `/api/article/get?page=${page + 1}&length=${rowsPerPage}`;
  //     if (searchTerm) url += `&q=${encodeURIComponent(searchTerm)}`;
  //     if (selectedUser) url += `&userId=${encodeURIComponent(selectedUser)}`;
  //     if (selectedCategory) url += `&CategoryId=${encodeURIComponent(selectedCategory)}`;
  //     if (orderBy) {
  //       const backendField = reverseFieldMapping[orderBy] || orderBy;
  //       url += `&orderBy=${encodeURIComponent(backendField)}&orderDir=${order}`;
  //     }

  //     const response = await axios.get(url, { withCredentials: true });
  //     const articlesData = response.data.data || [];
  //     const totalItems = response.data.pagination?.recordsFiltered || 0;

  //     const mappedArticles = articlesData.map(mapResponseToFrontend);
  //     setArticles(mappedArticles);
  //     setTotalCount(totalItems);
  //   } catch (error) {
  //     console.error("Error fetching articles:", error);
  //     if (typeof window !== "undefined") {
  //       await Swal.fire({
  //         icon: "error",
  //         title: "Error",
  //         text: error?.response?.data?.error || "Failed to fetch articles",
  //         timer: 3000,
  //         showConfirmButton: false,
  //       });
  //     }
  //     setArticles([]);
  //     setTotalCount(0);
  //   } finally {
  //     setShowLoader(false);
  //   }
  // }, [page, rowsPerPage, searchTerm, selectedUser, selectedCategory, orderBy, order]);
  const fetchArticles = useCallback(async () => {
    try {
      setShowLoader(true);
      let url = `/api/article/get?page=${page + 1}&length=${rowsPerPage}`;
      if (searchTerm) url += `&q=${encodeURIComponent(searchTerm)}`;
      if (selectedUser) url += `&userId=${encodeURIComponent(selectedUser)}`;
      if (selectedCategory)
        url += `&CategoryId=${encodeURIComponent(selectedCategory)}`;
      if (orderBy) {
        const backendField = reverseFieldMapping[orderBy] || orderBy;
        url += `&orderBy=${encodeURIComponent(backendField)}&orderDir=${order}`;
      }

      const response = await axios.get(url, { withCredentials: true });
      const articlesData = response.data.data || [];
      const totalItems = response.data.pagination?.recordsFiltered || 0;

      const mappedArticles = articlesData.map(mapResponseToFrontend);
      setArticles(mappedArticles);
      setTotalCount(totalItems);
    } catch (error) {
      console.error("Error fetching articles:", error);
      if (typeof window !== "undefined") {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to fetch articles",
          timer: 3000,
          showConfirmButton: false,
        });
      }
      setArticles([]);
      setTotalCount(0);
    } finally {
      setShowLoader(false);
    }
  }, [
    page,
    rowsPerPage,
    searchTerm,
    selectedUser,
    selectedCategory,
    orderBy,
    order,
  ]);
  const fetchCategories = useCallback(async (params = {}) => {
    try {
      setShowLoader(true);
      const response = await axios.get("/api/category/getAllCategory", {
        params: { q: params.search || "", _: new Date().getTime() },
        withCredentials: true,
      });
      if (response.status === 200) {
        setCategories(response.data.data);
      }
    } catch (error) {
      console.error("Error fetching categories:", error);
      if (typeof window !== "undefined") {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to fetch categories",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    } finally {
      setShowLoader(false);
    }
  }, []);

  const fetchCampaigns = useCallback(async (subdomainId) => {
    if (!subdomainId) {
      setCampaigns([]);
      setSelectedCampaignIds([]);
      setFormData((prev) => ({
        ...prev,
        campaigns: { val: [], err: "" },
      }));
      return;
    }

    try {
      const response = await axios.get(`/api/Campaigns/GetDropdown?subdomainId=${subdomainId}`, {
        withCredentials: true,
      });
      setCampaigns(response.data.data || []);
    } catch (error) {
      console.error("Error fetching campaigns:", error);
      if (typeof window !== "undefined") {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to fetch campaigns",
          timer: 3000,
          showConfirmButton: false,
        });
      }
      setCampaigns([]);
    }
  }, []);

  const fetchStyleIds = useCallback(async () => {
    try {
      const response = await axios.get("/api/StyleIds/get", {
        withCredentials: true,
      });
      setStyleIds(response.data.data);
    } catch (error) {
      if (typeof window !== "undefined") {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to fetch style IDs",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    }
  }, []);

  const fetchUsers = async () => {
    try {
      const response = await axios.get("/api/adminuser/GetDropdown", {
        withCredentials: true,
      });
      setUsers(response.data.data);
    } catch (error) {
      if (typeof window !== "undefined") {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: "Failed to fetch users",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    }
  };

  const fetchChannels = useCallback(async () => {
    try {
      const response = await axios.get("/api/Channals/GetDropdown", {
        withCredentials: true,
      });
      setAssignChannels(response.data.data);
    } catch (error) {
      if (typeof window !== "undefined") {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to fetch channels",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    }
  }, []);

  const fetchSettings = useCallback(async () => {
    try {
      const response = await axios.post("/api/AdminSetting/Get", {
        withCredentials: true,
      });
      const data = response.data.data;
      setSettingsData(data);
      if (data.dmStyleId === data.lmStyleId) {
        setFormData((prev) => ({
          ...prev,
          styleId: { val: data.lmStyleId, err: "" },
        }));
      }
    } catch (error) {
      if (typeof window !== "undefined") {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to fetch settings",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    }
  }, []);

  const fetchDomains = async () => {
    try {
      const response = await axios.get("/api/Domain/GetDropDown", {
        withCredentials: true,
      });
      setDomains(response.data.data);
    } catch (error) {
      if (typeof window !== "undefined") {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to load domains",
          timer: 3000,
          showConfirmButton: false,
        });
      }
    }
  };

  const fetchSubdomains = async () => {
    if (!selectedDomain) {
      setSubdomains([]);
      return;
    }
    try {
      const response = await axios.get(
        `/api/SubDomain/GetDropDown?DomainId=${selectedDomain}`,
        { withCredentials: true },
      );
      setSubdomains(response.data.data || []);
    } catch (error) {
      if (typeof window !== "undefined") {
        await Swal.fire({
          icon: "error",
          title: "Error",
          text: error?.response?.data?.error || "Failed to load subdomains",
          timer: 3000,
          showConfirmButton: false,
        });
      }
      setSubdomains([]);
      setSelectedSubdomain("");
    }
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault();

    // Validate required fields
    const updatedFormData = {
      ...formData,
      title: {
        val: formData.title.val,
        err: validateField("title", formData.title.val),
      },
      category: {
        val: formData.category.val,
        err: validateField("category", formData.category.val),
      },
      description: {
        val: description,
        err: validateField("description", description),
      },
      shortDescription: {
        val: shortDescription,
        err: validateField("shortDescription", shortDescription),
      },
      domain: {
        val: selectedDomain,
        err: validateField("domain", selectedDomain),
      },
      subdomain: {
        val: selectedSubdomain,
        err: validateField("subdomain", selectedSubdomain),
      },
      customChannal: {
        val: formData.customChannal.val,
        err: validateField("customChannal", formData.customChannal.val),
      },
    };

    const hasErrors = Object.values(updatedFormData).some((field) => field.err);
    if (hasErrors) {
      setFormData(updatedFormData);
      await Swal.fire({
        icon: "error",
        title: "Validation Error",
        text: "Please fill all required fields",
        timer: 3000,
        showConfirmButton: false,
      });
      return;
    }

    setShowLoader(true);
    try {
      const formDataPayload = new FormData();

      // Add all required fields with backend-expected names
      formDataPayload.append("Title", formData.title.val);
      formDataPayload.append("Category", formData.category.val);

      // Handle URL - ensure it's just the slug part
      const urlSlug = formData.url.val.split('/').pop() || autoslug(formData.title.val);
      formDataPayload.append("Url", urlSlug);

      // Add other fields only if they have values
      if (description) formDataPayload.append("Description", description);
      if (shortDescription) formDataPayload.append("ShortDescription", shortDescription);
      if (formData.metatitle.val) formDataPayload.append("MetaTitle", formData.metatitle.val);
      if (formData.metadescription.val) formDataPayload.append("MetaDescription", formData.metadescription.val);
      if (formData.metakeys.val) formDataPayload.append("MetaKeys", formData.metakeys.val);
      if (formData.customChannal.val) formDataPayload.append("CustomChannal", formData.customChannal.val);
      if (formData.styleId.val) formDataPayload.append("StyleId", formData.styleId.val);
      formDataPayload.append("AdRelatedSearches", formData.adrelatedsearches.val || "7");
      if (formData.remark.val) formDataPayload.append("Remark", formData.remark.val);
      if (formData.hashtag.val) formDataPayload.append("Hashtag", formData.hashtag.val);

      // Add boolean fields
      formDataPayload.append("Published", String(published));
      formDataPayload.append("ShowArticle", String(showArticle));
      formDataPayload.append("ShowsAds", String(showAds));

      // Add domain/subdomain
      if (selectedDomain) formDataPayload.append("Domain", selectedDomain);
      if (selectedSubdomain) formDataPayload.append("SubDomain", selectedSubdomain);

      // Add image if present
      if (formdataImage && typeof formdataImage === "object") {
        formDataPayload.append("file", formdataImage);
      }

      // Add edit ID if in edit mode
      if (editId) {
        formDataPayload.append("Id", editId);
      }

      // Add campaign IDs as JSON string
      formDataPayload.append("CampaignIds", JSON.stringify(selectedCampaignIds));

      const endpoint = editId ? "/api/article/edit" : "/api/article/add";
      const method = editId ? "PUT" : "POST";

      const response = await axios({
        url: endpoint,
        method,
        data: formDataPayload,
        headers: { "Content-Type": "multipart/form-data" },
        withCredentials: true,
      });

      if ([200, 201].includes(response.status)) {
        resetFormState();
        await Swal.fire({
          icon: "success",
          title: "Success",
          text: editId ? "Article updated successfully" : "Article created successfully",
          timer: 3000,
          showConfirmButton: false,
        });
        fetchArticles();
        setModalShow(false);
      }
    } catch (error) {
      console.error("Error saving article:", error.response?.data || error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: error?.response?.data?.error || "Failed to save article",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  };

  const resetFormState = () => {
    setEditId(null);
    setFormData(defaultValuesForm);
    setDescription("");
    setShortDescription("");
    setFormdataImage("");
    setBase64Image("");
    setPublished(true);
    setShowArticle(false);
    setShowAds(false);
    setSelectedDomain("");
    setSelectedSubdomain("");
    setSelectedCampaignIds([]);
    setSearchTerm("");
    setEditSlugMode(false);
  };

  const handleChange = (e) => {
    const { name, value, files } = e.target || e;
    if (name === "title") {
      const shouldUpdateSlug = !editId || editSlugMode;
      setFormData((prev) => ({
        ...prev,
        [name]: { val: value, err: validateField(name, value) },
        ...(shouldUpdateSlug && { url: { val: autoslug(value), err: "" } }),
      }));
      return;
    }
    if (name === "url") {
      setFormData((prev) => ({
        ...prev,
        url: { val: autoslug(value), err: "" },
      }));
      return;
    }
    if (files && files.length > 0) {
      setFormdataImage(files[0]);
      setBase64Image("");
      return;
    }
    setFormData((prev) => ({
      ...prev,
      [name]: { val: value, err: validateField(name, value) },
    }));
  };

  const handleCampaignSelect = (campaignIds) => {
    const numericIds = campaignIds.map(id => Number(id));
    setSelectedCampaignIds(numericIds);
    setFormData(prev => ({
      ...prev,
      campaigns: { val: numericIds, err: "" },
    }));
  };

  const handleDescriptionChange = (newContent) => {
    setDescription(newContent);
    setFormData((prev) => ({
      ...prev,
      description: {
        val: newContent,
        err: validateField("description", newContent),
      },
    }));
  };

  const handleShortDescriptionChange = (newContent) => {
    setShortDescription(newContent);
    setFormData((prev) => ({
      ...prev,
      shortDescription: {
        val: newContent,
        err: validateField("shortDescription", newContent),
      },
    }));
  };

  const handleChannelSelect = (channel) => {
    setFormData((prev) => ({
      ...prev,
      customChannal: {
        val: channel?.Id || "",
        err: validateField("customChannal", channel?.Id || ""),
      },
    }));
  };

  // const handleEditArticle = useCallback(
  //   (rowData) => {
  //     // if (!rowData || !rowData.id) {
  //     //   if (typeof window !== "undefined") {
  //     //     Swal.fire({
  //     //       icon: "error",
  //     //       title: "Error",
  //     //       text: "Article data is invalid. Please try again.",
  //     //       timer: 3000,
  //     //       showConfirmButton: false,
  //     //     });
  //     //   }
  //     //   return;
  //     // }

  //     const selectedChannel = assignChannels.find(
  //       (channel) => channel.CustomChannelId === rowData.customChannal?.Id
  //     );
  //     const channelId = selectedChannel ? selectedChannel.Id : "";

  //     setModalShow(true);
  //     setEditId(rowData.id);
  //     setEditSlugMode(false);

  //     const updatedFormData = {
  //       ...defaultValuesForm,
  //       title: { val: rowData.title || "", err: "" },
  //       category: { val: rowData.category?.Id || "", err: "" },
  //       url: { val: rowData.url || "", err: "" },
  //       description: { val: rowData.description || "", err: "" },
  //       shortDescription: { val: rowData.shortDescription || "", err: "" },
  //       metatitle: { val: rowData.metatitle || "", err: "" },
  //       metadescription: { val: rowData.metadescription || "", err: "" },
  //       metakeys: { val: rowData.metakeys || "", err: "" },
  //       hashtag: { val: rowData.hashtag || "", err: "" },
  //       customChannal: { val: channelId, err: "" },
  //       styleId: { val: rowData.styleId || "", err: "" },
  //       adrelatedsearches: { val: rowData.adrelatedsearches || "7", err: "" },
  //       remark: { val: rowData.remark || "", err: "" },
  //       campaigns: { val: rowData.campaigns?.map((c) => c.SNo) || [], err: "" },
  //       domain: { val: rowData.domain?.Id || "", err: "" },
  //       subdomain: { val: rowData.subdomain?.Id || "", err: "" },
  //     };

  //     setPublished(!!rowData.published);
  //     setShowArticle(!!rowData.showArticle);
  //     setShowAds(!!rowData.showAds);
  //     setFormdataImage(
  //       rowData.image
  //         ? fetchImage(
  //             rowData.image.includes("cloudinary") ? "cloud" : "small",
  //             rowData.image
  //           )
  //         : ""
  //     );
  //     setBase64Image("");
  //     setSelectedDomain(rowData.domain?.Id || "");
  //     setSelectedSubdomain(rowData.subdomain?.Id || "");
  //     setDescription(rowData.description || "");
  //     setShortDescription(rowData.shortDescription || "");
  //     setSelectedCampaignIds(rowData.campaigns?.map((c) => c.SNo) || []);
  //     setFormData(updatedFormData);
  //   },
  //   [categories, assignChannels, styleIds, domains, subdomains, campaigns]
  // );
  const handleEditArticle = useCallback(async (rowData) => {
    try {
      setShowLoader(true);

      // Ensure required data is loaded before proceeding
      const loadPromises = [];

      if (assignChannels.length === 0) {
        loadPromises.push(fetchChannels());
      }
      if (domains.length === 0) {
        loadPromises.push(fetchDomains());
      }
      if (styleIds.length === 0) {
        loadPromises.push(fetchStyleIds());
      }

      // Wait for all data to be loaded
      await Promise.all(loadPromises);

      const response = await axios.get(`/api/article/GetById?id=${rowData.id}`, {
        withCredentials: true,
      });

      if (response.data.success && response.data.data?.length > 0) {
        const article = response.data.data[0];

        // Debug: Log the article data to see what we're getting
        console.log("Article data from API:", article);
        console.log("Available channels:", assignChannels);
        console.log("Available domains:", domains);
        console.log("Available styleIds:", styleIds);

        setModalShow(true);
        setEditId(article.Id);
        setEditSlugMode(false);

        // Find the channel ID from assignChannels based on the article's custom channel
        // Use ChannelId from the transformed response
        const channelId = article.ChannelId || "";

        // Set boolean states first
        setPublished(!!article.Published);
        setShowArticle(!!article.ShowArticle);
        setShowAds(!!article.ShowsAds);

        // Set editor content
        setDescription(article.Description || "");
        setShortDescription(article.ShortDescription || "");

        // Use setTimeout to ensure all state updates are processed before setting form data
        setTimeout(() => {
          // Initialize form data using the actual article data from API
          const formDataToSet = {
            ...defaultValuesForm,
            title: { val: article.Title || "", err: "" },
            category: { val: article.CategoryId || "", err: "" },
            url: { val: article.Url || "", err: "" },
            description: { val: article.Description || "", err: "" },
            shortDescription: { val: article.ShortDescription || "", err: "" },
            metatitle: { val: article.MetaTitle || "", err: "" },
            metadescription: { val: article.MetaDescription || "", err: "" },
            metakeys: { val: article.MetaKeys || "", err: "" },
            hashtag: { val: article.Hashtag || "", err: "" },
            customChannal: { val: channelId, err: "" },
            styleId: { val: article.StyleId || "", err: "" },
            adrelatedsearches: { val: article.AdRelatedSearches || "7", err: "" },
            remark: { val: article.Remark || "", err: "" },
            campaigns: { val: article.Campaigns?.map((c) => c.SNo) || [], err: "" },
            domain: { val: article.DomainId || "", err: "" },
            subdomain: { val: article.SubDomainId || "", err: "" },
          };

          console.log("Form data being set:", formDataToSet);
          console.log("StyleIds available when setting form:", styleIds);

          // Check if the article's StyleId exists in the available styleIds
          const styleExists = styleIds.find(style => style.Id === article.StyleId);
          console.log("Style exists in options:", styleExists);
          console.log("Article StyleId:", article.StyleId);
          console.log("Article StyleId type:", typeof article.StyleId);
          console.log("StyleIds sample:", styleIds.slice(0, 3).map(s => ({ Id: s.Id, IdType: typeof s.Id, Name: s.Name })));

          setFormData(formDataToSet);
        }, 100);

        // Set domain and subdomain selections using the correct field names
        console.log("Setting domain:", article.DomainId);
        console.log("Setting subdomain:", article.SubDomainId);
        setSelectedDomain(article.DomainId || "");

        // Load subdomains for the selected domain, then set subdomain
        if (article.DomainId) {
          try {
            const subdomainResponse = await axios.get(
              `/api/SubDomain/GetDropDown?DomainId=${article.DomainId}`,
              { withCredentials: true }
            );
            console.log("Loaded subdomains:", subdomainResponse.data.data);
            setSubdomains(subdomainResponse.data.data || []);
            setSelectedSubdomain(article.SubDomainId || "");
          } catch (error) {
            console.error("Error loading subdomains:", error);
            setSubdomains([]);
            setSelectedSubdomain("");
          }
        } else {
          setSelectedSubdomain("");
        }

        // Initialize campaigns if subdomain exists
        if (article.SubDomainId) {
          await fetchCampaigns(article.SubDomainId);
          console.log("Article campaigns:", article.Campaigns);
          if (article.Campaigns && article.Campaigns.length > 0) {
            const campaignIds = article.Campaigns.map(c => c.SNo);
            console.log("Setting campaign IDs:", campaignIds);
            setSelectedCampaignIds(campaignIds);
          }
        } else {
          setSelectedCampaignIds([]);
        }

        // Handle image
        if (article.Image) {
          setFormdataImage(fetchImage(
            article.Image.includes("cloudinary") ? "cloud" : "small",
            article.Image
          ));
        } else {
          setFormdataImage("");
        }
        setBase64Image("");
      }
    } catch (error) {
      console.error("Error fetching article:", error);
      await Swal.fire({
        icon: "error",
        title: "Error",
        text: "Failed to load article data",
        timer: 3000,
        showConfirmButton: false,
      });
    } finally {
      setShowLoader(false);
    }
  }, [fetchCampaigns, assignChannels, domains, styleIds, fetchChannels, fetchDomains, fetchStyleIds]);
  const handleDeleteArticle = useCallback(
    async (rowData) => {
      if (typeof window !== "undefined") {
        const result = await Swal.fire({
          icon: "warning",
          title: "Confirm Deletion",
          text: "Are you sure you want to delete this article?",
          showCancelButton: true,
          confirmButtonColor: "#5750f1",
          cancelButtonColor: "#d33",
          showCloseButton: true,
          confirmButtonText: "Yes, delete it!",
        });

        if (result.isConfirmed) {
          try {
            setShowLoader(true);
            await axios.delete("/api/article/delete", {
              data: { id: rowData.id },
              withCredentials: true,
              headers: { "Content-Type": "application/json" },
            });
            if (typeof window !== "undefined") {
              await Swal.fire({
                icon: "success",
                title: "Success",
                text: "Article deleted successfully",
                timer: 3000,
                showConfirmButton: false,
              });
            }
            fetchArticles();
          } catch (error) {
            console.error("Error deleting article:", error);
            if (typeof window !== "undefined") {
              await Swal.fire({
                icon: "error",
                title: "Error",
                text:
                  error?.response?.data?.error || "Failed to delete article",
                timer: 3000,
                showConfirmButton: false,
              });
            }
          } finally {
            setShowLoader(false);
          }
        }
      }
    },
    [fetchArticles],
  );

  const handleViewArticle = useCallback((rowData) => {
    if (!rowData.published) {
      if (typeof window !== "undefined") {
        Swal.fire({
          icon: "warning",
          title: "Warning",
          text: "Article is not published yet",
          timer: 3000,
          showConfirmButton: false,
        });
      }
      return;
    }
    const link = makeLinkRedirect(rowData);
    if (typeof window !== "undefined") {
      window.open(link, "_blank");
    }
  }, []);

  const makeLinkRedirect = (article) => {
    if (!article.published) return "/";
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL_CLIENT_USER_SITE;

    if (article.domain?.showUrlName) {
      const fullDomain = article.subdomain?.name
        ? `${article.subdomain.name}.${article.domain.showUrlName}`
        : article.domain.showUrlName;
      const slug = article.url.split("/").pop();
      const channelId =
        article.customChannal?.reportingDimensionId?.split(":")[1] || "";
      return `https://${fullDomain}/${slug}?channel=${channelId}&mode=light`;
    }

    const channelId =
      article.customChannal?.reportingDimensionId?.split(":")[1] || "";
    return `${baseUrl}/${article.url.split("/").pop()}?channel=${channelId}&mode=light`;
  };

  const autoslug = (title) =>
    title ? title.replace(/[^a-zA-Z0-9]+/g, "-").toLowerCase() : "";

  const imageConvert = async () => {
    if (formdataImage && typeof formdataImage === "object") {
      try {
        const base64 = await convertToBase64(formdataImage);
        setBase64Image(base64);
      } catch (error) {
        console.error("Failed to convert image:", error);
        setBase64Image("");
        if (typeof window !== "undefined") {
          await Swal.fire({
            icon: "error",
            title: "Error",
            text: "Failed to process image. Please select a valid image file.",
            timer: 3000,
            showConfirmButton: false,
          });
        }
      }
    }
  };

  const renderSlugField = () => (
    <div className="mb-4">
      <label className="mb-2 block text-sm font-medium text-dark dark:text-white">
        URL
      </label>
      <div className="flex flex-wrap items-center gap-2">
        <span className="rounded-md border border-stroke bg-gray-100 px-4 py-3 text-dark dark:border-dark-3 dark:bg-dark-2 dark:text-white">
          {selectedDomain
            ? `https://${
                selectedSubdomain
                  ? `${subdomains.find((s) => s.Id === selectedSubdomain)?.Name || ""}.`
                  : ""
              }${domains.find((d) => d.Id === selectedDomain)?.Name || ""}/`
            : `${process.env.NEXT_PUBLIC_BASE_URL_CLIENT_USER_SITE}/`}
        </span>
        <input
          type="text"
          name="url"
          value={formData.url.val.split("/").pop()}
          onChange={handleChange}
          className="min-w-[200px] flex-1 rounded-lg border border-stroke bg-transparent px-5 py-3 outline-none transition-all duration-200 focus:border-primary focus:ring-2 focus:ring-primary/20 dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary"
          readOnly={!editSlugMode}
        />
        <label className="flex items-center gap-2">
          <input
            type="checkbox"
            checked={editSlugMode}
            onChange={() => setEditSlugMode(!editSlugMode)}
            className="rounded border-stroke text-primary focus:ring-primary dark:border-dark-3"
          />
          <span className="text-dark dark:text-white">Edit Slug</span>
        </label>
      </div>
      {formData.url.err && (
        <p className="mt-1 text-sm text-red-500">{formData.url.err}</p>
      )}
    </div>
  );

  useEffect(() => {
    fetchCategories();
    fetchUsers();
    fetchArticles();
  }, [fetchArticles]);

  useEffect(() => {
    if (modalShow) {
      fetchChannels();
      fetchSettings();
      fetchStyleIds();
      fetchDomains();
    }
  }, [modalShow, fetchChannels, fetchSettings, fetchStyleIds]);

  useEffect(() => {
    fetchSubdomains();
  }, [selectedDomain]);
  useEffect(() => {
    if (selectedSubdomain) {
      fetchCampaigns(selectedSubdomain);
    } else {
      setCampaigns([]);
      setSelectedCampaignIds([]);
      setFormData((prev) => ({
        ...prev,
        campaigns: { val: [], err: "" },
      }));
    }
  }, [selectedSubdomain, fetchCampaigns]);

  useEffect(() => {
    imageConvert();
  }, [formdataImage]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        // Handle dropdown close if needed
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const styleForModal = {
    position: "absolute",
    top: "50%",
    left: "50%",
    border: "none",
    transform: "translate(-50%, -50%)",
    width: { xs: "70%", sm: "70%", md: "50%", lg: "70%" },
    bgcolor: "background.paper",
    boxShadow: 24,
    p: 4,
    borderRadius: "16px",
    maxHeight: { xs: "90vh", md: "85vh" },
    overflowY: "auto",
    scrollbarWidth: "none",
  };

  return (
    <>
      <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-6">
        <div className="mb-5 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
          <h2 className="text-2xl font-bold text-dark dark:text-white">
            Articles
          </h2>
          <div className="flex flex-col items-center gap-4 sm:flex-row">
            {isSuperAdmin && (
              <div className="w-full sm:w-64">
                <SearchableDropdown
                  // label="Filter by User"
                  options={[{ Id: "", Name: "All Users" }, ...users]}
                  placeholder="Select User..."
                  value={selectedUser}
                  onChange={(user) => {
                    setSelectedUser(user?.Id || "");
                    setPage(0); // Reset to first page on filter change
                  }}
                  displayKey="Name"
                  idKey="Id"
                />
              </div>
            )}
            <div className="w-full sm:w-64">
              <SearchableDropdown
                // label="Filter by Category"
                options={[{ Id: "", Name: "All Categories" }, ...categories]}
                placeholder="Select Category..."
                value={selectedCategory}
                onChange={(category) => {
                  setSelectedCategory(category?.Id || "");
                  setPage(0); // Reset to first page on filter change
                }}
                displayKey="Name"
                idKey="Id"
              />
            </div>
            <Button
              type="button"
              label="Add Article"
              variant="primary"
              shape="rounded"
              className="flex items-center justify-center gap-2"
              icon={<FaPlus size={14} />}
              onClick={() => {
                setModalShow(true);
                resetFormState();
              }}
            />
          </div>
        </div>
        <CustomDataTable
          isLoading={showLoader}
          columns={columns}
          rows={articles}
          searchTerm={searchTerm}
          onSearchChange={setSearchTerm}
          page={page}
          rowsPerPage={rowsPerPage}
          onPageChange={setPage}
          onRequestSort={(_, property) => {
            const isAsc = orderBy === property && order === "asc";
            setOrder(isAsc ? "desc" : "asc");
            setOrderBy(property);
          }}
          onRowsPerPageChange={(value) => {
            setRowsPerPage(value);
            setPage(0);
          }}
          totalCount={totalCount}
          order={order}
          orderBy={orderBy}
          onView={handleViewArticle}
          onEdit={handleEditArticle}
          onDelete={handleDeleteArticle}
        />
      </div>
      <Modal
        open={modalShow}
        onClose={() => {
          setModalShow(false);
          resetFormState();
        }}
        aria-labelledby="article-modal-title"
        aria-describedby="article-modal-description"
        sx={{ width: "100%" }}
      >
        <Box sx={styleForModal}>
          <div className="mb-5 flex items-center justify-between">
            <h2 className="text-xl font-semibold text-gray-800">
              {editId ? "Edit Article" : "Add Article"}
            </h2>
            <button
              onClick={() => {
                setModalShow(false);
                resetFormState();
              }}
              className="text-gray-500 hover:text-gray-700 focus:outline-none"
              aria-label="Close modal"
            >
              <IoCloseOutline size={24} />
            </button>
          </div>
          <form onSubmit={handleFormSubmit} className="flex flex-1 flex-col">
            <div className="mb-4 flex flex-col gap-4 lg:flex-row">
              <SearchableDropdown
                label="Domain"
                options={domains}
                placeholder="Select Domain..."
                value={selectedDomain}
                onChange={(domain) => {
                  setSelectedDomain(domain?.Id || "");
                  setSelectedSubdomain("");
                  setFormData((prev) => ({
                    ...prev,
                    domain: {
                      val: domain?.Id || "",
                      err: validateField("domain", domain?.Id || ""),
                    },
                    subdomain: { val: "", err: "" },
                  }));
                }}
                error={formData.domain.err}
                displayKey="Name"
                idKey="Id"
                required
              />
              <SearchableDropdown
                label="Subdomain"
                options={subdomains}
                placeholder="Select Subdomain..."
                value={selectedSubdomain}
                onChange={(subdomain) => {
                  setSelectedSubdomain(subdomain?.Id || "");
                  setFormData((prev) => ({
                    ...prev,
                    subdomain: {
                      val: subdomain?.Id || "",
                      err: validateField("subdomain", subdomain?.Id || ""),
                    },
                  }));
                }}
                error={formData.subdomain.err}
                displayKey="Name"
                idKey="Id"
                disabled={!selectedDomain || !subdomains.length}
                required
              />
            </div>
            <div className="mb-4 flex flex-col gap-4 lg:flex-row">
              <div className="w-full">
                <InputGroup
                  label="Article Title"
                  name="title"
                  type="text"
                  placeholder="Enter Article Title"
                  value={formData.title.val}
                  handleChange={handleChange}
                  required
                  className="w-full"
                  active={!!formData.title.err}
                />
                {formData.title.err && (
                  <p className="mt-1 text-sm text-red-500">
                    {formData.title.err}
                  </p>
                )}
              </div>
              <div className="w-full">
                <SearchableDropdown
                  label="Category"
                  options={categories}
                  placeholder="Select Category..."
                  value={formData.category.val}
                  onChange={(category) => {
                    setFormData((prev) => ({
                      ...prev,
                      category: {
                        val: category?.Id || "",
                        err: validateField("category", category?.Id || ""),
                      },
                    }));
                  }}
                  error={formData.category.err}
                  displayKey="Name"
                  idKey="Id"
                  required
                />
                {formData.category.err && (
                  <p className="mt-1 text-sm text-red-500">
                    {formData.category.err}
                  </p>
                )}
              </div>
            </div>
            {renderSlugField()}
            <div className="mb-4">
              <label className="mb-2 block text-sm font-medium text-dark dark:text-white">
                Short Description
              </label>
              <JoditEditor
                value={shortDescription}
                config={shortJoditConfig}
                onBlur={handleShortDescriptionChange}
                onChange={() => {}}
              />
              {formData.shortDescription.err && (
                <p className="mt-1 text-sm text-red-500">
                  {formData.shortDescription.err}
                </p>
              )}
            </div>
            <div className="mb-4">
              <label className="mb-2 block text-sm font-medium text-dark dark:text-white">
                Description
              </label>
              <JoditEditor
                value={description}
                config={joditConfig}
                onBlur={handleDescriptionChange}
                onChange={() => {}}
              />
              {formData.description.err && (
                <p className="mt-1 text-sm text-red-500">
                  {formData.description.err}
                </p>
              )}
            </div>
            <div className="mb-4 flex flex-col gap-4 sm:flex-row">
              <div className="w-full sm:w-1/3">
                <TextAreaGroup
                  label="Meta Title"
                  name="metatitle"
                  value={formData.metatitle.val}
                  handleChange={handleChange}
                  placeholder="Enter Meta Title"
                  rows={2}
                />
              </div>
              <div className="w-full sm:w-1/3">
                <TextAreaGroup
                  label="Meta Description"
                  name="metadescription"
                  value={formData.metadescription.val}
                  handleChange={handleChange}
                  placeholder="Enter Meta Description"
                  rows={2}
                />
              </div>
              <div className="w-full sm:w-1/3">
                <TextAreaGroup
                  label="Meta Keys"
                  name="metakeys"
                  value={formData.metakeys.val}
                  handleChange={handleChange}
                  placeholder="Enter Meta Keys"
                  rows={2}
                />
              </div>
            </div>
            <div className="mb-4">
              <TextAreaGroup
                label="Hashtag"
                name="hashtag"
                value={formData.hashtag.val}
                handleChange={(e) =>
                  handleChange({
                    target: {
                      name: "hashtag",
                      value: e.target.value.replace(/[ ,]/g, "#"),
                    },
                  })
                }
                placeholder="Enter Hashtags"
                rows={2}
              />
            </div>
            <div className="mb-4 flex flex-col gap-4 lg:flex-row">
              <SearchableDropdown
                label="Channel Settings"
                options={assignChannels}
                placeholder="Select Channel..."
                value={formData.customChannal.val}
                onChange={handleChannelSelect}
                error={formData.customChannal.err}
                displayKey="DisplayName"
                idKey="Id"
                required
              />
              <SearchableDropdown
                label="Style Id"
                options={styleIds}
                placeholder="Search Style ID..."
                value={formData.styleId.val}
                onChange={(style) => {
                  setFormData((prev) => ({
                    ...prev,
                    styleId: {
                      val: style?.Id || "",
                      err: validateField("styleId", style?.Id || ""),
                    },
                  }));
                }}
                error={formData.styleId.err}
                displayKey="Name"
                idKey="Id"
              />
            </div>
            <div className="mb-4">
              <MultiSelectDropdown
                label="Campaigns"
                options={campaigns}
                value={selectedCampaignIds}
                onChange={handleCampaignSelect}
                placeholder="Search Campaigns..."
                displayKey="Name"
                idKey="SNo"
                showSelectAll={true}
                error={formData.campaigns.err}
              />
            </div>
            <div className="mb-4">
              <InputGroup
                label="Ad Related Searches"
                name="adrelatedsearches"
                type="number"
                value={formData.adrelatedsearches.val}
                handleChange={handleChange}
                placeholder="Enter number of ads"
                required
              />
            </div>
            <div className="mb-4">
              <TextAreaGroup
                label="Remark"
                name="remark"
                value={formData.remark.val}
                handleChange={handleChange}
                placeholder="Enter Remark"
                rows={3}
              />
            </div>
            <div className="mb-4">
              <label className="mb-2 block text-sm font-medium text-dark dark:text-white">
                Image
              </label>
              <input
                type="file"
                name="fileImage"
                accept="image/*"
                onChange={handleChange}
                className="w-full rounded-lg border border-stroke px-3 py-2"
              />
              {(base64Image ||
                (typeof formdataImage === "string" && formdataImage)) && (
                <div className="mt-4">
                  <img
                    src={base64Image || formdataImage}
                    alt="Preview"
                    className="h-32 w-32 rounded object-cover"
                    width={128}
                    height={128}
                  />
                </div>
              )}
            </div>
            <div className="mb-6 flex flex-col gap-4 sm:flex-row">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={published}
                  onChange={() => setPublished(!published)}
                  className="checkbox rounded"
                />
                <span className="text-dark dark:text-white">Published</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={showArticle}
                  onChange={() => setShowArticle(!showArticle)}
                  className="checkbox rounded"
                />
                <span className="text-dark dark:text-white">Show Article</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={showAds}
                  onChange={() => setShowAds(!showAds)}
                  className="checkbox rounded"
                />
                <span className="text-dark dark:text-white">Show Ads</span>
              </label>
            </div>
            <div className="flex justify-end gap-2">
              <Button
                type="button"
                label="Cancel"
                onClick={() => {
                  setModalShow(false);
                  resetFormState();
                }}
                variant="outlined"
                shape="rounded"
              />
              <Button
                type="submit"
                label={editId ? "Update Article" : "Add Article"}
                variant="primary"
                shape="rounded"
                disabled={showLoader}
              />
            </div>
          </form>
        </Box>
      </Modal>
    </>
  );
};

export default ArticlePage;