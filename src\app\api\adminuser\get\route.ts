import { prisma } from '../../../../lib/prisma';
import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '../../../../lib/varifyToken';

export async function GET(req: NextRequest) {
    try {
        const user = await verifyToken(req);
        if (!user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
        }

        const { searchParams } = new URL(req.url);
        const start = parseInt(searchParams.get("page") || "1");
        const length = parseInt(searchParams.get("length") || "10");
        const search = searchParams.get("q") || '';
        const orderBy = searchParams.get("orderBy") || "CreatedAt";
        const orderDir = searchParams.get("orderDir") || "asc";

        // const page = Math.floor(start / length) + 1;
        let skip: number | undefined;
        let limit: number | undefined;

        if (length == -1) {
            skip = undefined;
            limit = undefined;
        }
        else {
            const skipCount = (start == 1 ? 0 : start - 1) * length;
            limit = length;
            skip = (skipCount > 0 ? skipCount : 0);
        }

        let where: any = {
            IsDeleted: false,
        };
        if (search) {
            where.OR = [
                { Name: { contains: search, mode: 'insensitive' } },
                { Email: { contains: search, mode: 'insensitive' } },
                { Number: { contains: search } }
            ];
        }

        const countFields = ['subDomainCount', 'styleIdCount'];

        let orderByClause = {};
        if (!countFields.includes(orderBy)) {
            const allowedFields = ['Id', 'Name', 'Email', 'Number', 'User_Type', 'Status', 'AccessExpiration', 'CreatedAt'];
            if (allowedFields.includes(orderBy)) {
                orderByClause = { [orderBy]: orderDir as 'asc' | 'desc' };
            }
        }


        const recordsTotal = await prisma.adminUser.count({
            where: {
                IsDeleted: false
            }
        });

        const recordsFiltered = await prisma.adminUser.count({ where });

        const users = await prisma.adminUser.findMany({
            where,
            skip,
            take: limit,
            orderBy: Object.keys(orderByClause).length ? orderByClause : undefined,
            select: {
                Id: true,
                Name: true,
                Email: true,
                Number: true,
                User_Type: true,
                Status: true,
            }
        });
        const usersWithCounts = await Promise.all(
            users.map(async (user) => {
                const subDomainCount = await prisma.subDomainUserMappings.count({
                    where: { UserId: user.Id }
                });

                const styleIdCount = await prisma.styleIdUserMappings.count({
                    where: { UserId: user.Id }
                });

                return {
                    ...user,
                    subDomainCount,
                    styleIdCount
                };
            })
        );
        return NextResponse.json({
            success: true,
            data: usersWithCounts,
            pagination: {
                draw: parseInt(searchParams.get("draw") || "1"),
                //recordsTotal,
                recordsFiltered,
                currentPageCount: users.length,
                start,
                length,
                currentPage: start,
                totalPages: length === -1 ? 1 : Math.ceil(recordsFiltered / (length || 1)),
                hasNextPage: length === -1 ? false : start * length < recordsFiltered,
                hasPreviousPage: start > 1,
            }
        });

    } catch (error) {
        console.error("Error fetching users:", error);
        return NextResponse.json(
            { error: "User Not Found" },
            { status: 500 }
        );
    } finally {
        await prisma.$disconnect();
    }
}