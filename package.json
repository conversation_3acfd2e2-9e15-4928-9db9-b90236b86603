{"name": "free-nextadmin-nextjs", "version": "1.2.1", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "seed": "node prisma/seed.js"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.0", "@mui/material": "^7.1.0", "@prisma/client": "^6.7.0", "axios": "^1.9.0", "bcrypt": "^6.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "flatpickr": "^4.6.13", "googleapis": "^149.0.0", "install": "^0.13.0", "jodit-react": "^5.2.19", "jose": "^6.0.11", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jsvectormap": "^1.6.0", "next": "15.1.6", "next-themes": "^0.4.4", "nextjs-toploader": "^3.7.15", "node-fetch": "^3.3.2", "npm": "^11.4.1", "prisma": "^6.7.0", "react": "19.0.0", "react-dom": "19.0.0", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "sharp": "^0.34.2", "sweetalert2": "^11.21.0", "tailwind-merge": "^2.6.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.9", "@types/node": "^22", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.1.6", "postcss": "^8.5.3", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.17", "typescript": "^5"}}